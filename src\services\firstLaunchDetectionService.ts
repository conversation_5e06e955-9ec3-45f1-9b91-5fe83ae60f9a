/**
 * 首次启动检测服务
 * 负责检测应用是否为首次启动，并管理相关状态
 */

export interface FirstLaunchInfo {
  isFirstLaunch: boolean;
  installationDate?: string;
  lastLaunchDate?: string;
  launchCount: number;
  version: string;
  needsPrivacyConsent: boolean;
  needsActivation: boolean;
  reason: string;
}

export interface LaunchHistory {
  installDate: string;
  firstLaunchDate: string;
  lastLaunchDate: string;
  launchCount: number;
  versions: string[];
  currentVersion: string;
}

class FirstLaunchDetectionService {
  private static instance: FirstLaunchDetectionService;
  private readonly STORAGE_KEY = 'hout_launch_history';
  private readonly PRIVACY_VERSION_KEY = 'hout_privacy_version';
  private readonly CURRENT_PRIVACY_VERSION = '1.0.0';

  public static getInstance(): FirstLaunchDetectionService {
    if (!FirstLaunchDetectionService.instance) {
      FirstLaunchDetectionService.instance = new FirstLaunchDetectionService();
    }
    return FirstLaunchDetectionService.instance;
  }

  /**
   * 检测首次启动状态
   */
  public async detectFirstLaunch(): Promise<FirstLaunchInfo> {
    try {
      const currentVersion = await this.getCurrentVersion();
      const launchHistory = this.getLaunchHistory();
      const privacyVersion = this.getPrivacyPolicyVersion();
      
      const now = new Date().toISOString();
      let isFirstLaunch = false;
      let needsPrivacyConsent = false;
      let needsActivation = true;
      let reason = '';

      if (!launchHistory) {
        // 真正的首次启动
        isFirstLaunch = true;
        needsPrivacyConsent = true;
        needsActivation = true;
        reason = '应用首次安装启动';

        // 创建启动历史记录
        const newHistory: LaunchHistory = {
          installDate: now,
          firstLaunchDate: now,
          lastLaunchDate: now,
          launchCount: 1,
          versions: [currentVersion],
          currentVersion,
        };

        this.saveLaunchHistory(newHistory);
        this.savePrivacyPolicyVersion(this.CURRENT_PRIVACY_VERSION);

      } else {
        // 非首次启动，但需要检查各种条件
        const updatedHistory: LaunchHistory = {
          ...launchHistory,
          lastLaunchDate: now,
          launchCount: launchHistory.launchCount + 1,
          currentVersion,
          versions: launchHistory.versions.includes(currentVersion) 
            ? launchHistory.versions 
            : [...launchHistory.versions, currentVersion],
        };

        this.saveLaunchHistory(updatedHistory);

        // 检查是否需要重新同意隐私政策
        if (!privacyVersion || privacyVersion !== this.CURRENT_PRIVACY_VERSION) {
          needsPrivacyConsent = true;
          reason = '隐私政策版本更新，需要重新同意';
          this.savePrivacyPolicyVersion(this.CURRENT_PRIVACY_VERSION);
        }

        // 检查版本更新
        if (launchHistory.currentVersion !== currentVersion) {
          reason = reason || `版本从 ${launchHistory.currentVersion} 更新到 ${currentVersion}`;
        }

        // 检查激活状态
        const hasValidActivation = this.checkActivationStatus();
        if (hasValidActivation) {
          needsActivation = false;
          reason = reason || '正常启动，激活有效';
        } else {
          needsActivation = true;
          reason = reason || '激活状态无效或已过期';
        }

        if (!reason) {
          reason = '正常启动';
        }
      }

      const result: FirstLaunchInfo = {
        isFirstLaunch,
        installationDate: launchHistory?.installDate,
        lastLaunchDate: launchHistory?.lastLaunchDate,
        launchCount: launchHistory?.launchCount || 1,
        version: currentVersion,
        needsPrivacyConsent,
        needsActivation,
        reason,
      };

      console.log('🔍 首次启动检测结果:', result);
      return result;

    } catch (error) {
      console.error('首次启动检测失败:', error);
      
      // 发生错误时，采用保守策略
      return {
        isFirstLaunch: true,
        launchCount: 1,
        version: '1.0.0',
        needsPrivacyConsent: true,
        needsActivation: true,
        reason: '检测失败，采用首次启动模式',
      };
    }
  }

  /**
   * 获取当前应用版本
   */
  private async getCurrentVersion(): Promise<string> {
    try {
      const { getVersion } = await import('@tauri-apps/api/app');
      return await getVersion();
    } catch (error) {
      console.warn('无法获取Tauri版本，使用默认版本号');
      return '1.0.0';
    }
  }

  /**
   * 获取启动历史记录
   */
  private getLaunchHistory(): LaunchHistory | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return null;
      
      const parsed = JSON.parse(stored);
      
      // 验证数据结构
      if (this.isValidLaunchHistory(parsed)) {
        return parsed;
      } else {
        console.warn('启动历史记录格式无效，将重新创建');
        localStorage.removeItem(this.STORAGE_KEY);
        return null;
      }
    } catch (error) {
      console.error('读取启动历史记录失败:', error);
      return null;
    }
  }

  /**
   * 保存启动历史记录
   */
  private saveLaunchHistory(history: LaunchHistory): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(history));
    } catch (error) {
      console.error('保存启动历史记录失败:', error);
    }
  }

  /**
   * 验证启动历史记录格式
   */
  private isValidLaunchHistory(data: any): data is LaunchHistory {
    return (
      data &&
      typeof data.installDate === 'string' &&
      typeof data.firstLaunchDate === 'string' &&
      typeof data.lastLaunchDate === 'string' &&
      typeof data.launchCount === 'number' &&
      Array.isArray(data.versions) &&
      typeof data.currentVersion === 'string'
    );
  }

  /**
   * 获取隐私政策版本
   */
  private getPrivacyPolicyVersion(): string | null {
    try {
      return localStorage.getItem(this.PRIVACY_VERSION_KEY);
    } catch (error) {
      console.error('读取隐私政策版本失败:', error);
      return null;
    }
  }

  /**
   * 保存隐私政策版本
   */
  private savePrivacyPolicyVersion(version: string): void {
    try {
      localStorage.setItem(this.PRIVACY_VERSION_KEY, version);
    } catch (error) {
      console.error('保存隐私政策版本失败:', error);
    }
  }

  /**
   * 检查激活状态
   */
  private checkActivationStatus(): boolean {
    try {
      const activationData = localStorage.getItem('wanjiguanjia_activation_data');
      if (!activationData) return false;

      const parsed = JSON.parse(activationData);
      
      // 检查激活是否有效且未过期
      if (parsed.isActivated && parsed.expiresAt) {
        const expiryDate = new Date(parsed.expiresAt);
        const now = new Date();
        return now < expiryDate;
      }

      return false;
    } catch (error) {
      console.error('检查激活状态失败:', error);
      return false;
    }
  }

  /**
   * 重置首次启动状态（用于测试）
   */
  public resetFirstLaunchState(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.PRIVACY_VERSION_KEY);
      console.log('首次启动状态已重置');
    } catch (error) {
      console.error('重置首次启动状态失败:', error);
    }
  }

  /**
   * 获取启动统计信息
   */
  public getStartupStatistics(): {
    totalLaunches: number;
    daysSinceInstall: number;
    daysSinceLastLaunch: number;
    versionsUsed: string[];
  } | null {
    const history = this.getLaunchHistory();
    if (!history) return null;

    const now = new Date();
    const installDate = new Date(history.installDate);
    const lastLaunchDate = new Date(history.lastLaunchDate);

    const daysSinceInstall = Math.floor((now.getTime() - installDate.getTime()) / (1000 * 60 * 60 * 24));
    const daysSinceLastLaunch = Math.floor((now.getTime() - lastLaunchDate.getTime()) / (1000 * 60 * 60 * 24));

    return {
      totalLaunches: history.launchCount,
      daysSinceInstall,
      daysSinceLastLaunch,
      versionsUsed: history.versions,
    };
  }
}

export const firstLaunchDetectionService = FirstLaunchDetectionService.getInstance();
export default firstLaunchDetectionService;
