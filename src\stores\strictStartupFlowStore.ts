/**
 * 严格启动流程状态管理
 * 支持流程恢复、错误回滚和状态持久化
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// 严格启动阶段定义
export type StrictStartupPhase =
  | 'network-check'           // 网络连接检查
  | 'version-check'           // 版本检查和公告显示
  | 'first-launch-detection'  // 首次启动检测
  | 'privacy-consent'         // 隐私政策确认
  | 'activation-verification' // 激活码验证
  | 'main-app'                // 进入主界面
  | 'data-collection'         // 数据收集初始化
  | 'completed'               // 完成
  | 'error';                  // 错误状态

// 阶段状态
export interface PhaseStatus {
  phase: StrictStartupPhase;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startTime?: number;
  endTime?: number;
  duration?: number;
  error?: string;
  retryCount: number;
  data?: any;
}

// 启动流程状态
export interface StrictStartupFlowState {
  // 当前状态
  currentPhase: StrictStartupPhase;
  isInitialized: boolean;
  isCompleted: boolean;
  hasError: boolean;
  
  // 阶段状态跟踪
  phaseStatuses: Record<StrictStartupPhase, PhaseStatus>;
  
  // 流程数据
  networkResult?: any;
  versionCheckResult?: any;
  firstLaunchInfo?: any;
  privacyConsentData?: any;
  activationData?: any;
  
  // 错误处理
  lastError?: string;
  errorPhase?: StrictStartupPhase;
  canRecover: boolean;
  
  // 流程控制
  allowSkip: boolean;
  forceRestart: boolean;
  
  // 会话信息
  sessionId: string;
  startTime: number;
  lastUpdateTime: number;
}

// 启动流程操作
export interface StrictStartupFlowActions {
  // 流程控制
  initializeFlow: () => void;
  setCurrentPhase: (phase: StrictStartupPhase) => void;
  completePhase: (phase: StrictStartupPhase, data?: any) => void;
  failPhase: (phase: StrictStartupPhase, error: string) => void;
  retryPhase: (phase: StrictStartupPhase) => void;
  
  // 数据管理
  setNetworkResult: (result: any) => void;
  setVersionCheckResult: (result: any) => void;
  setFirstLaunchInfo: (info: any) => void;
  setPrivacyConsentData: (data: any) => void;
  setActivationData: (data: any) => void;
  
  // 错误处理
  setError: (error: string, phase?: StrictStartupPhase) => void;
  clearError: () => void;
  canRecoverFromError: () => boolean;
  recoverFromError: () => void;
  
  // 流程恢复
  saveCheckpoint: () => void;
  restoreFromCheckpoint: () => boolean;
  resetFlow: () => void;
  
  // 状态查询
  getPhaseStatus: (phase: StrictStartupPhase) => PhaseStatus;
  isPhaseCompleted: (phase: StrictStartupPhase) => boolean;
  canProceedToPhase: (phase: StrictStartupPhase) => boolean;
  getFlowProgress: () => number;
  
  // 调试和监控
  getFlowSummary: () => any;
  exportFlowData: () => string;
}

// 阶段顺序定义
const PHASE_ORDER: StrictStartupPhase[] = [
  'network-check',
  'version-check',
  'first-launch-detection',
  'privacy-consent',
  'activation-verification',
  'main-app',
  'data-collection',
  'completed',
];

// 初始阶段状态
const createInitialPhaseStatus = (phase: StrictStartupPhase): PhaseStatus => ({
  phase,
  status: 'pending',
  retryCount: 0,
});

// 初始状态
const initialState: StrictStartupFlowState = {
  currentPhase: 'network-check',
  isInitialized: false,
  isCompleted: false,
  hasError: false,
  
  phaseStatuses: PHASE_ORDER.reduce((acc, phase) => {
    acc[phase] = createInitialPhaseStatus(phase);
    return acc;
  }, {} as Record<StrictStartupPhase, PhaseStatus>),
  
  canRecover: false,
  allowSkip: false,
  forceRestart: false,
  
  sessionId: '',
  startTime: 0,
  lastUpdateTime: 0,
};

// 生成会话ID
const generateSessionId = (): string => {
  return `startup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 创建状态管理器
export const useStrictStartupFlowStore = create<StrictStartupFlowState & StrictStartupFlowActions>()(
  persist(
    (set, get) => ({
      ...initialState,

      // 流程控制
      initializeFlow: () => {
        const now = Date.now();
        const sessionId = generateSessionId();
        
        console.log(`🚀 初始化严格启动流程 [${sessionId}]`);
        
        set({
          ...initialState,
          isInitialized: true,
          sessionId,
          startTime: now,
          lastUpdateTime: now,
          phaseStatuses: {
            ...initialState.phaseStatuses,
            'network-check': {
              ...initialState.phaseStatuses['network-check'],
              status: 'running',
              startTime: now,
            },
          },
        });
      },

      setCurrentPhase: (phase: StrictStartupPhase) => {
        const now = Date.now();
        const state = get();
        
        console.log(`🔄 切换到阶段: ${phase}`);
        
        // 更新当前阶段状态为运行中
        const updatedStatuses = { ...state.phaseStatuses };
        updatedStatuses[phase] = {
          ...updatedStatuses[phase],
          status: 'running',
          startTime: now,
        };
        
        set({
          currentPhase: phase,
          lastUpdateTime: now,
          phaseStatuses: updatedStatuses,
        });
      },

      completePhase: (phase: StrictStartupPhase, data?: any) => {
        const now = Date.now();
        const state = get();
        
        console.log(`✅ 完成阶段: ${phase}`);
        
        const phaseStatus = state.phaseStatuses[phase];
        const duration = phaseStatus.startTime ? now - phaseStatus.startTime : 0;
        
        const updatedStatuses = { ...state.phaseStatuses };
        updatedStatuses[phase] = {
          ...phaseStatus,
          status: 'completed',
          endTime: now,
          duration,
          data,
        };
        
        // 检查是否完成所有阶段
        const isCompleted = phase === 'completed';
        
        set({
          phaseStatuses: updatedStatuses,
          lastUpdateTime: now,
          isCompleted,
          ...(data && { [`${phase}Result`]: data }),
        });
      },

      failPhase: (phase: StrictStartupPhase, error: string) => {
        const now = Date.now();
        const state = get();
        
        console.error(`❌ 阶段失败: ${phase} - ${error}`);
        
        const phaseStatus = state.phaseStatuses[phase];
        const duration = phaseStatus.startTime ? now - phaseStatus.startTime : 0;
        
        const updatedStatuses = { ...state.phaseStatuses };
        updatedStatuses[phase] = {
          ...phaseStatus,
          status: 'failed',
          endTime: now,
          duration,
          error,
        };
        
        set({
          phaseStatuses: updatedStatuses,
          hasError: true,
          lastError: error,
          errorPhase: phase,
          lastUpdateTime: now,
          canRecover: get().canRecoverFromError(),
        });
      },

      retryPhase: (phase: StrictStartupPhase) => {
        const now = Date.now();
        const state = get();
        
        console.log(`🔄 重试阶段: ${phase}`);
        
        const phaseStatus = state.phaseStatuses[phase];
        const updatedStatuses = { ...state.phaseStatuses };
        updatedStatuses[phase] = {
          ...phaseStatus,
          status: 'running',
          startTime: now,
          retryCount: phaseStatus.retryCount + 1,
          error: undefined,
        };
        
        set({
          currentPhase: phase,
          phaseStatuses: updatedStatuses,
          hasError: false,
          lastError: undefined,
          errorPhase: undefined,
          lastUpdateTime: now,
        });
      },

      // 数据管理
      setNetworkResult: (result: any) => {
        set({ networkResult: result, lastUpdateTime: Date.now() });
      },

      setVersionCheckResult: (result: any) => {
        set({ versionCheckResult: result, lastUpdateTime: Date.now() });
      },

      setFirstLaunchInfo: (info: any) => {
        set({ firstLaunchInfo: info, lastUpdateTime: Date.now() });
      },

      setPrivacyConsentData: (data: any) => {
        set({ privacyConsentData: data, lastUpdateTime: Date.now() });
      },

      setActivationData: (data: any) => {
        set({ activationData: data, lastUpdateTime: Date.now() });
      },

      // 错误处理
      setError: (error: string, phase?: StrictStartupPhase) => {
        console.error(`❌ 启动流程错误: ${error}`);
        set({
          hasError: true,
          lastError: error,
          errorPhase: phase || get().currentPhase,
          lastUpdateTime: Date.now(),
        });
      },

      clearError: () => {
        set({
          hasError: false,
          lastError: undefined,
          errorPhase: undefined,
          canRecover: false,
          lastUpdateTime: Date.now(),
        });
      },

      canRecoverFromError: () => {
        const state = get();
        if (!state.hasError || !state.errorPhase) return false;
        
        // 网络检查和版本检查失败不允许恢复
        const nonRecoverablePhases: StrictStartupPhase[] = ['network-check', 'version-check'];
        return !nonRecoverablePhases.includes(state.errorPhase);
      },

      recoverFromError: () => {
        const state = get();
        if (!state.canRecoverFromError()) return;
        
        console.log(`🔧 从错误中恢复: ${state.errorPhase}`);
        
        if (state.errorPhase) {
          get().retryPhase(state.errorPhase);
        }
      },

      // 流程恢复
      saveCheckpoint: () => {
        const state = get();
        const checkpoint = {
          currentPhase: state.currentPhase,
          phaseStatuses: state.phaseStatuses,
          networkResult: state.networkResult,
          versionCheckResult: state.versionCheckResult,
          firstLaunchInfo: state.firstLaunchInfo,
          timestamp: Date.now(),
        };
        
        try {
          localStorage.setItem('hout_startup_checkpoint', JSON.stringify(checkpoint));
          console.log('💾 启动流程检查点已保存');
        } catch (error) {
          console.error('保存检查点失败:', error);
        }
      },

      restoreFromCheckpoint: () => {
        try {
          const stored = localStorage.getItem('hout_startup_checkpoint');
          if (!stored) return false;
          
          const checkpoint = JSON.parse(stored);
          const now = Date.now();
          
          // 检查检查点是否过期（1小时）
          if (now - checkpoint.timestamp > 3600000) {
            localStorage.removeItem('hout_startup_checkpoint');
            return false;
          }
          
          console.log('🔄 从检查点恢复启动流程');
          
          set({
            currentPhase: checkpoint.currentPhase,
            phaseStatuses: checkpoint.phaseStatuses,
            networkResult: checkpoint.networkResult,
            versionCheckResult: checkpoint.versionCheckResult,
            firstLaunchInfo: checkpoint.firstLaunchInfo,
            lastUpdateTime: now,
          });
          
          return true;
        } catch (error) {
          console.error('从检查点恢复失败:', error);
          return false;
        }
      },

      resetFlow: () => {
        console.log('🔄 重置启动流程');
        
        try {
          localStorage.removeItem('hout_startup_checkpoint');
        } catch (error) {
          console.error('清理检查点失败:', error);
        }
        
        set({
          ...initialState,
          sessionId: generateSessionId(),
          startTime: Date.now(),
          lastUpdateTime: Date.now(),
        });
      },

      // 状态查询
      getPhaseStatus: (phase: StrictStartupPhase) => {
        return get().phaseStatuses[phase];
      },

      isPhaseCompleted: (phase: StrictStartupPhase) => {
        return get().phaseStatuses[phase].status === 'completed';
      },

      canProceedToPhase: (phase: StrictStartupPhase) => {
        const state = get();
        const phaseIndex = PHASE_ORDER.indexOf(phase);
        
        if (phaseIndex === 0) return true;
        
        // 检查前面的阶段是否都已完成
        for (let i = 0; i < phaseIndex; i++) {
          const prevPhase = PHASE_ORDER[i];
          if (!state.isPhaseCompleted(prevPhase)) {
            return false;
          }
        }
        
        return true;
      },

      getFlowProgress: () => {
        const state = get();
        const completedPhases = PHASE_ORDER.filter(phase => 
          state.isPhaseCompleted(phase)
        ).length;
        
        return (completedPhases / PHASE_ORDER.length) * 100;
      },

      // 调试和监控
      getFlowSummary: () => {
        const state = get();
        const now = Date.now();
        
        return {
          sessionId: state.sessionId,
          currentPhase: state.currentPhase,
          progress: state.getFlowProgress(),
          duration: now - state.startTime,
          isCompleted: state.isCompleted,
          hasError: state.hasError,
          lastError: state.lastError,
          phaseCount: PHASE_ORDER.length,
          completedPhases: PHASE_ORDER.filter(phase => state.isPhaseCompleted(phase)),
          failedPhases: PHASE_ORDER.filter(phase => state.phaseStatuses[phase].status === 'failed'),
        };
      },

      exportFlowData: () => {
        const state = get();
        const summary = state.getFlowSummary();
        
        return JSON.stringify({
          summary,
          phaseStatuses: state.phaseStatuses,
          data: {
            networkResult: state.networkResult,
            versionCheckResult: state.versionCheckResult,
            firstLaunchInfo: state.firstLaunchInfo,
            privacyConsentData: state.privacyConsentData,
            activationData: state.activationData,
          },
        }, null, 2);
      },
    }),
    {
      name: 'hout-strict-startup-flow-storage',
      storage: createJSONStorage(() => localStorage),
      // 只持久化关键状态，不持久化运行时数据
      partialize: (state) => ({
        sessionId: state.sessionId,
        startTime: state.startTime,
        networkResult: state.networkResult,
        versionCheckResult: state.versionCheckResult,
        firstLaunchInfo: state.firstLaunchInfo,
        privacyConsentData: state.privacyConsentData,
        activationData: state.activationData,
      }),
    }
  )
);

export default useStrictStartupFlowStore;
