/**
 * 严格网络连接检查组件
 * 不允许降级策略和离线模式，网络检查失败直接退出应用
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  makeStyles,
  Text,
  Title2,
  Button,
  Spinner,
  MessageBar,
  MessageBarBody,
  ProgressBar,
  Card,
  tokens,
} from '@fluentui/react-components';
import {
  Wifi24Regular,
  WifiOff24Regular,
  ArrowClockwise24Regular,
  Warning24Regular,
  Checkmark24Regular,
  Dismiss24Regular,
} from '@fluentui/react-icons';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100vh',
    padding: tokens.spacingVerticalXXL,
    backgroundColor: tokens.colorNeutralBackground1,
  },
  card: {
    maxWidth: '500px',
    width: '100%',
    padding: tokens.spacingVerticalXL,
    textAlign: 'center',
  },
  icon: {
    fontSize: '48px',
    marginBottom: tokens.spacingVerticalL,
  },
  title: {
    marginBottom: tokens.spacingVerticalM,
  },
  description: {
    marginBottom: tokens.spacingVerticalL,
    color: tokens.colorNeutralForeground2,
  },
  progressContainer: {
    width: '100%',
    marginBottom: tokens.spacingVerticalL,
  },
  statusText: {
    marginBottom: tokens.spacingVerticalS,
    fontSize: tokens.fontSizeBase200,
  },
  actions: {
    display: 'flex',
    gap: tokens.spacingHorizontalM,
    justifyContent: 'center',
    marginTop: tokens.spacingVerticalL,
  },
  messageBar: {
    marginBottom: tokens.spacingVerticalL,
  },
  exitWarning: {
    marginTop: tokens.spacingVerticalL,
    padding: tokens.spacingVerticalM,
    backgroundColor: tokens.colorPaletteRedBackground1,
    borderRadius: tokens.borderRadiusMedium,
    border: `1px solid ${tokens.colorPaletteRedBorder1}`,
  },
});

export interface NetworkCheckResult {
  isConnected: boolean;
  latency?: number;
  canReachAPI: boolean;
  timestamp: number;
  error?: string;
}

interface StrictNetworkCheckerProps {
  onComplete: (result: NetworkCheckResult) => void;
  onError: (error: string) => void;
  maxRetries?: number;
  retryInterval?: number;
}

const StrictNetworkChecker: React.FC<StrictNetworkCheckerProps> = ({
  onComplete,
  onError,
  maxRetries = 3,
  retryInterval = 3000,
}) => {
  const styles = useStyles();
  const [isChecking, setIsChecking] = useState(true);
  const [currentAttempt, setCurrentAttempt] = useState(1);
  const [status, setStatus] = useState('正在检查网络连接...');
  const [result, setResult] = useState<NetworkCheckResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [showExitWarning, setShowExitWarning] = useState(false);

  // 检查网络连接
  const checkNetworkConnection = useCallback(async (): Promise<NetworkCheckResult> => {
    const startTime = Date.now();
    
    try {
      // 1. 检查基本网络连接
      setStatus('检查设备网络状态...');
      setProgress(20);
      
      const isOnline = navigator.onLine;
      if (!isOnline) {
        throw new Error('设备未连接到网络');
      }

      // 2. 检查互联网连接
      setStatus('检查互联网连接...');
      setProgress(40);
      
      // 尝试连接多个公共DNS服务器
      const connectivityTests = [
        fetch('https://8.8.8.8', { method: 'HEAD', mode: 'no-cors', cache: 'no-cache' }),
        fetch('https://1.1.1.1', { method: 'HEAD', mode: 'no-cors', cache: 'no-cache' }),
      ];

      await Promise.any(connectivityTests).catch(() => {
        throw new Error('无法连接到互联网');
      });

      // 3. 检查API服务器连接
      setStatus('检查API服务器连接...');
      setProgress(70);
      
      const apiUrl = 'https://api-g.lacs.cc/health';
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      try {
        const apiResponse = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Cache-Control': 'no-cache',
          },
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!apiResponse.ok) {
          throw new Error(`API服务器响应错误: ${apiResponse.status}`);
        }

        const latency = Date.now() - startTime;
        setProgress(100);
        setStatus('网络连接检查完成');

        return {
          isConnected: true,
          latency,
          canReachAPI: true,
          timestamp: Date.now(),
        };

      } catch (fetchError) {
        clearTimeout(timeoutId);
        throw new Error('API服务器不可达');
      }

    } catch (error) {
      const latency = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : '网络连接失败';
      
      return {
        isConnected: false,
        latency,
        canReachAPI: false,
        timestamp: Date.now(),
        error: errorMessage,
      };
    }
  }, []);

  // 执行网络检查
  const performNetworkCheck = useCallback(async (attemptNumber: number = 1) => {
    setIsChecking(true);
    setCurrentAttempt(attemptNumber);
    setError(null);
    setProgress(0);
    setShowExitWarning(false);

    try {
      const checkResult = await checkNetworkConnection();
      setResult(checkResult);

      if (checkResult.isConnected && checkResult.canReachAPI) {
        // 网络连接正常
        setStatus('网络连接正常，准备继续启动流程');
        setTimeout(() => {
          onComplete(checkResult);
        }, 1000);
      } else if (attemptNumber < maxRetries) {
        // 连接失败，准备重试
        setError(`网络连接失败：${checkResult.error || '未知错误'}`);
        setStatus(`第 ${attemptNumber} 次尝试失败，${retryInterval / 1000} 秒后重试...`);
        
        setTimeout(() => {
          performNetworkCheck(attemptNumber + 1);
        }, retryInterval);
      } else {
        // 达到最大重试次数，准备退出应用
        const finalError = `网络连接失败：${checkResult.error || '未知错误'}`;
        setError(finalError);
        setStatus('网络连接检查失败');
        setIsChecking(false);
        setShowExitWarning(true);

        // 3秒后自动退出应用
        setTimeout(async () => {
          try {
            const { exit } = await import('@tauri-apps/api/app');
            await exit(1);
          } catch (exitError) {
            console.error('退出应用失败:', exitError);
            window.close();
          }
        }, 3000);

        onError(`${finalError}\n\n已尝试 ${maxRetries} 次，应用将在3秒后退出`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '网络检查异常';
      setError(errorMessage);
      setStatus('网络检查异常');
      setIsChecking(false);
      setShowExitWarning(true);

      setTimeout(async () => {
        try {
          const { exit } = await import('@tauri-apps/api/app');
          await exit(1);
        } catch (exitError) {
          console.error('退出应用失败:', exitError);
          window.close();
        }
      }, 3000);

      onError(`${errorMessage}\n\n应用将在3秒后退出`);
    }
  }, [checkNetworkConnection, maxRetries, retryInterval, onComplete, onError]);

  // 组件挂载时开始检查
  useEffect(() => {
    performNetworkCheck();
  }, [performNetworkCheck]);

  // 手动重试
  const handleRetry = () => {
    performNetworkCheck(1);
  };

  // 立即退出应用
  const handleExitNow = async () => {
    try {
      const { exit } = await import('@tauri-apps/api/app');
      await exit(1);
    } catch (exitError) {
      console.error('退出应用失败:', exitError);
      window.close();
    }
  };

  const renderIcon = () => {
    if (isChecking) {
      return <Spinner size="extra-large" />;
    } else if (result?.isConnected) {
      return <Checkmark24Regular className={styles.icon} style={{ color: tokens.colorPaletteGreenForeground1 }} />;
    } else {
      return <WifiOff24Regular className={styles.icon} style={{ color: tokens.colorPaletteRedForeground1 }} />;
    }
  };

  const renderContent = () => {
    if (isChecking) {
      return (
        <>
          <Title2 className={styles.title}>网络连接检查</Title2>
          <Text className={styles.description}>
            正在检查网络连接状态，请稍候...
          </Text>
          <div className={styles.progressContainer}>
            <Text className={styles.statusText}>{status}</Text>
            <ProgressBar value={progress / 100} />
            <Text className={styles.statusText}>
              尝试次数：{currentAttempt} / {maxRetries}
            </Text>
          </div>
        </>
      );
    } else if (result?.isConnected) {
      return (
        <>
          <Title2 className={styles.title}>网络连接正常</Title2>
          <Text className={styles.description}>
            网络连接检查完成，延迟：{result.latency}ms
          </Text>
          <MessageBar intent="success" className={styles.messageBar}>
            <MessageBarBody>
              ✅ 网络连接正常，API服务器可达
            </MessageBarBody>
          </MessageBar>
        </>
      );
    } else {
      return (
        <>
          <Title2 className={styles.title}>网络连接失败</Title2>
          <Text className={styles.description}>
            无法连接到网络或API服务器，应用无法正常运行
          </Text>
          {error && (
            <MessageBar intent="error" className={styles.messageBar}>
              <MessageBarBody>
                <Warning24Regular /> {error}
              </MessageBarBody>
            </MessageBar>
          )}
          
          {showExitWarning && (
            <div className={styles.exitWarning}>
              <Text weight="semibold" style={{ color: tokens.colorPaletteRedForeground1 }}>
                ⚠️ 网络连接是应用正常运行的必要条件
              </Text>
              <Text style={{ marginTop: tokens.spacingVerticalS, color: tokens.colorPaletteRedForeground1 }}>
                应用将在3秒后自动退出
              </Text>
            </div>
          )}

          <div className={styles.actions}>
            <Button
              appearance="primary"
              icon={<ArrowClockwise24Regular />}
              onClick={handleRetry}
              disabled={showExitWarning}
            >
              重试连接
            </Button>
            {showExitWarning && (
              <Button
                appearance="secondary"
                icon={<Dismiss24Regular />}
                onClick={handleExitNow}
              >
                立即退出
              </Button>
            )}
          </div>
        </>
      );
    }
  };

  return (
    <div className={styles.container}>
      <Card className={styles.card}>
        {renderIcon()}
        {renderContent()}
      </Card>
    </div>
  );
};

export default StrictNetworkChecker;
