/**
 * 严格版本检查组件
 * 不支持降级策略，版本检查失败直接退出应用
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  makeStyles,
  Text,
  Title2,
  <PERSON><PERSON>,
  Spinner,
  MessageBar,
  MessageBarBody,
  ProgressBar,
  Card,
  tokens,
} from '@fluentui/react-components';
import {
  CheckmarkCircle24Regular,
  Warning24Regular,
  ArrowClockwise24Regular,
  WifiOff24Regular,
  Shield24Regular,
} from '@fluentui/react-icons';
import { useStartupFlowStore } from '../../stores/startupFlowStore';
import { getCurrentVersion, checkLatestVersion } from '../../services/versionService';
import { SecurityConfigManager } from '../../config/securityConfig';
// import { networkService, NetworkCheckResult } from '../../services/networkService';
import { VersionCheckResult } from '../../types/version';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100vh',
    padding: tokens.spacingVerticalXXL,
    backgroundColor: tokens.colorNeutralBackground1,
  },
  card: {
    maxWidth: '600px',
    width: '100%',
    padding: tokens.spacingVerticalXL,
    textAlign: 'center',
  },
  icon: {
    fontSize: '48px',
    marginBottom: tokens.spacingVerticalL,
  },
  title: {
    marginBottom: tokens.spacingVerticalM,
  },
  description: {
    marginBottom: tokens.spacingVerticalL,
    color: tokens.colorNeutralForeground2,
  },
  progressContainer: {
    width: '100%',
    marginBottom: tokens.spacingVerticalL,
  },
  statusText: {
    marginBottom: tokens.spacingVerticalS,
    fontSize: tokens.fontSizeBase200,
  },
  actions: {
    display: 'flex',
    gap: tokens.spacingHorizontalM,
    justifyContent: 'center',
    marginTop: tokens.spacingVerticalL,
  },
  messageBar: {
    marginBottom: tokens.spacingVerticalL,
  },
  networkInfo: {
    marginTop: tokens.spacingVerticalM,
    padding: tokens.spacingVerticalM,
    backgroundColor: tokens.colorNeutralBackground2,
    borderRadius: tokens.borderRadiusMedium,
  },
});

interface StrictVersionCheckerProps {
  onComplete: (result: VersionCheckResult) => void;
  onError: (error: string) => void;
  networkResult?: any; // NetworkCheckResult;
}

type CheckPhase = 'network' | 'security' | 'version' | 'complete' | 'error';

const StrictVersionChecker: React.FC<StrictVersionCheckerProps> = ({
  onComplete,
  onError,
  networkResult,
}) => {
  const styles = useStyles();
  const [phase, setPhase] = useState<CheckPhase>('network');
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState('正在检查网络连接...');
  const [result, setResult] = useState<VersionCheckResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [networkInfo, setNetworkInfo] = useState<NetworkCheckResult | null>(networkResult || null);

  const {
    setVersionCheckResult,
    setVersionCheckCompleted,
    setError: setStoreError,
    resetRetryCount,
  } = useStartupFlowStore();

  const maxRetries = 3;

  // 执行完整的版本检查流程
  const performVersionCheck = useCallback(async (attempt: number = 1) => {
    setRetryCount(attempt);
    setError(null);
    setProgress(0);

    try {
      // 阶段1：网络检查（如果没有提供网络结果）
      if (!networkInfo) {
        setPhase('network');
        setStatus('检查网络连接状态...');
        setProgress(10);

        // 临时跳过网络检查，使用传入的网络结果
        // const netResult = await networkService.performFullNetworkCheck();
        // setNetworkInfo(netResult);

        // if (!netResult.overall.isReady) {
        //   throw new Error(`网络连接失败: ${netResult.connectivity.error || '无法连接到服务器'}`);
        // }
      }

      // 阶段2：安全配置初始化
      setPhase('security');
      setStatus('初始化安全配置...');
      setProgress(30);

      const configManager = SecurityConfigManager.getInstance();
      await configManager.initialize();

      // 阶段3：版本检查
      setPhase('version');
      setStatus('获取当前版本信息...');
      setProgress(50);

      const currentVersion = await getCurrentVersion();

      setStatus('检查最新版本...');
      setProgress(70);

      const versionResult = await checkLatestVersion(currentVersion);

      setProgress(90);
      setStatus('处理版本检查结果...');

      // 阶段4：完成
      setPhase('complete');
      setProgress(100);
      setStatus('版本检查完成');
      setResult(versionResult);

      // 更新状态管理
      setVersionCheckResult(versionResult);
      setVersionCheckCompleted(true);
      resetRetryCount();

      // 延迟完成，让用户看到结果
      setTimeout(() => {
        onComplete(versionResult);
      }, 2000);

    } catch (error) {
      console.error(`❌ 版本检查失败 (尝试 ${attempt}/${maxRetries}):`, error);
      
      const errorMessage = error instanceof Error ? error.message : '版本检查失败';
      setError(errorMessage);
      setPhase('error');

      if (attempt < maxRetries) {
        // 准备重试
        setStatus(`第 ${attempt} 次尝试失败，准备重试...`);
        setTimeout(() => {
          performVersionCheck(attempt + 1);
        }, 3000);
      } else {
        // 达到最大重试次数，退出应用
        setStoreError(errorMessage);

        // 3秒后自动退出应用
        setTimeout(async () => {
          try {
            const { exit } = await import('@tauri-apps/api/app');
            await exit(1);
          } catch (exitError) {
            console.error('退出应用失败:', exitError);
            window.close();
          }
        }, 3000);

        onError(`版本检查失败: ${errorMessage}\n\n已尝试 ${maxRetries} 次，应用将在3秒后退出`);
      }
    }
  }, [networkInfo, maxRetries, onComplete, onError, setVersionCheckResult, setVersionCheckCompleted, resetRetryCount, setStoreError]);

  // 组件挂载时开始检查
  useEffect(() => {
    performVersionCheck();
  }, [performVersionCheck]);

  // 手动重试
  const handleRetry = () => {
    performVersionCheck(1);
  };

  // 立即退出应用
  const handleExitNow = async () => {
    try {
      const { exit } = await import('@tauri-apps/api/app');
      await exit(1);
    } catch (exitError) {
      console.error('退出应用失败:', exitError);
      window.close();
    }
  };

  const renderIcon = () => {
    switch (phase) {
      case 'network':
      case 'security':
      case 'version':
        return <Spinner size="extra-large" />;
      case 'complete':
        return <CheckmarkCircle24Regular className={styles.icon} style={{ color: tokens.colorPaletteGreenForeground1 }} />;
      case 'error':
        return <Warning24Regular className={styles.icon} style={{ color: tokens.colorPaletteRedForeground1 }} />;
      default:
        return <Shield24Regular className={styles.icon} />;
    }
  };

  const renderContent = () => {
    switch (phase) {
      case 'network':
      case 'security':
      case 'version':
        return (
          <>
            <Title2 className={styles.title}>版本检查</Title2>
            <Text className={styles.description}>
              正在检查应用版本和安全配置，请稍候...
            </Text>
            <div className={styles.progressContainer}>
              <Text className={styles.statusText}>{status}</Text>
              <ProgressBar value={progress / 100} />
              <Text className={styles.statusText}>
                尝试次数：{retryCount} / {maxRetries}
              </Text>
            </div>
          </>
        );

      case 'complete':
        return (
          <>
            <Title2 className={styles.title}>版本检查完成</Title2>
            <Text className={styles.description}>
              {result?.isLatest ? '当前已是最新版本' : '发现新版本可用'}
            </Text>
            <MessageBar intent="success" className={styles.messageBar}>
              <MessageBarBody>
                ✅ 版本检查成功，准备继续启动流程
              </MessageBarBody>
            </MessageBar>
          </>
        );



      case 'error':
        return (
          <>
            <Title2 className={styles.title}>版本检查失败</Title2>
            <Text className={styles.description}>
              无法完成版本检查，请检查网络连接或稍后重试
            </Text>
            {error && (
              <MessageBar intent="error" className={styles.messageBar}>
                <MessageBarBody>
                  ❌ {error}
                </MessageBarBody>
              </MessageBar>
            )}
            <div className={styles.actions}>
              <Button
                appearance="primary"
                icon={<ArrowClockwise24Regular />}
                onClick={handleRetry}
              >
                重试检查
              </Button>
              <Button
                appearance="secondary"
                icon={<Warning24Regular />}
                onClick={handleExitNow}
              >
                退出应用
              </Button>
            </div>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <div className={styles.container}>
      <Card className={styles.card}>
        {renderIcon()}
        {renderContent()}
        
        {networkInfo && (
          <div className={styles.networkInfo}>
            <Text>
              网络状态: {networkInfo.overall.quality} | 
              延迟: {networkInfo.connectivity.latency}ms | 
              API: {networkInfo.apiHealth.isHealthy ? '正常' : '异常'}
            </Text>
          </div>
        )}
      </Card>
    </div>
  );
};

export default StrictVersionChecker;
