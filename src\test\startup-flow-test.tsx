/**
 * 严格启动流程测试文件
 * 用于验证启动流程修复效果
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock 必要的服务
jest.mock('../services/logService', () => ({
  logService: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

jest.mock('../services/networkService', () => ({
  networkService: {
    performFullNetworkCheck: jest.fn().mockResolvedValue({
      overall: { isReady: true },
      connectivity: { isConnected: true, latency: 100 },
      apiHealth: { isHealthy: true, responseTime: 200 },
    }),
  },
}));

jest.mock('../services/versionService', () => ({
  getCurrentVersion: jest.fn().mockResolvedValue('1.0.0'),
  checkLatestVersion: jest.fn().mockResolvedValue({
    isLatest: true,
    currentVersion: '1.0.0',
    isValid: true,
  }),
}));

jest.mock('../services/firstLaunchDetectionService', () => ({
  firstLaunchDetectionService: {
    detectFirstLaunch: jest.fn().mockResolvedValue({
      isFirstLaunch: false,
      needsPrivacyConsent: false,
      needsActivation: false,
      reason: '正常启动',
    }),
  },
}));

jest.mock('../services/enhancedActivationService', () => ({
  enhancedActivationService: {
    verifyCurrentActivation: jest.fn().mockResolvedValue({
      isValid: true,
      isActivated: true,
      isExpired: false,
      deviceBound: true,
      securityLevel: 'high',
      warnings: [],
      errors: [],
    }),
  },
}));

// Mock Tauri API
jest.mock('@tauri-apps/api/app', () => ({
  exit: jest.fn(),
  getVersion: jest.fn().mockResolvedValue('1.0.0'),
}));

jest.mock('@tauri-apps/api/os', () => ({
  platform: jest.fn().mockResolvedValue('win32'),
  arch: jest.fn().mockResolvedValue('x86_64'),
  version: jest.fn().mockResolvedValue('10.0.19041'),
}));

describe('严格启动流程测试', () => {
  beforeEach(() => {
    // 清理本地存储
    localStorage.clear();
    
    // 重置所有 mock
    jest.clearAllMocks();
    
    // 设置测试环境
    (window as any).__HOUT_TEST_MODE__ = true;
  });

  afterEach(() => {
    // 清理测试环境
    delete (window as any).__HOUT_TEST_MODE__;
    localStorage.clear();
  });

  test('应用启动时应显示加载界面', async () => {
    render(<App />);
    
    // 应该显示加载界面
    expect(screen.getByText('正在加载玩机管家')).toBeInTheDocument();
    expect(screen.getByText('正在初始化应用...')).toBeInTheDocument();
  });

  test('初始化完成后应显示严格启动流程', async () => {
    render(<App />);
    
    // 等待初始化完成
    await waitFor(() => {
      expect(screen.queryByText('正在加载玩机管家')).not.toBeInTheDocument();
    }, { timeout: 2000 });
    
    // 应该显示严格启动流程
    await waitFor(() => {
      expect(screen.getByText('网络连接检查') || screen.getByText('网络检查')).toBeInTheDocument();
    }, { timeout: 1000 });
  });

  test('网络检查失败时应显示错误并退出', async () => {
    // Mock 网络检查失败
    const { networkService } = require('../services/networkService');
    networkService.performFullNetworkCheck.mockResolvedValue({
      overall: { isReady: false },
      connectivity: { isConnected: false, error: '网络连接失败' },
      apiHealth: { isHealthy: false, error: 'API不可达' },
    });

    render(<App />);
    
    // 等待网络检查失败
    await waitFor(() => {
      expect(screen.getByText(/网络连接失败/i) || screen.getByText(/网络不可用/i)).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  test('版本检查失败时应显示错误并退出', async () => {
    // Mock 版本检查失败
    const { checkLatestVersion } = require('../services/versionService');
    checkLatestVersion.mockRejectedValue(new Error('版本检查失败'));

    render(<App />);
    
    // 等待版本检查失败
    await waitFor(() => {
      expect(screen.getByText(/版本检查失败/i) || screen.getByText(/版本验证失败/i)).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  test('首次启动时应显示隐私政策确认', async () => {
    // Mock 首次启动
    const { firstLaunchDetectionService } = require('../services/firstLaunchDetectionService');
    firstLaunchDetectionService.detectFirstLaunch.mockResolvedValue({
      isFirstLaunch: true,
      needsPrivacyConsent: true,
      needsActivation: true,
      reason: '应用首次安装启动',
    });

    render(<App />);
    
    // 等待隐私政策确认界面
    await waitFor(() => {
      expect(screen.getByText(/隐私政策/i) || screen.getByText(/用户协议/i)).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  test('激活验证失败时应显示错误', async () => {
    // Mock 激活验证失败
    const { enhancedActivationService } = require('../services/enhancedActivationService');
    enhancedActivationService.verifyCurrentActivation.mockResolvedValue({
      isValid: false,
      isActivated: false,
      isExpired: false,
      deviceBound: false,
      securityLevel: 'low',
      warnings: [],
      errors: ['激活码无效'],
    });

    render(<App />);
    
    // 等待激活验证失败
    await waitFor(() => {
      expect(screen.getByText(/激活/i)).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  test('正常启动流程应按顺序执行所有阶段', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    
    render(<App />);
    
    // 等待启动流程完成
    await waitFor(() => {
      // 检查控制台日志中是否包含各个阶段
      const logs = consoleSpy.mock.calls.map(call => call.join(' '));
      
      expect(logs.some(log => log.includes('网络检查') || log.includes('network-check'))).toBe(true);
      expect(logs.some(log => log.includes('版本检查') || log.includes('version-check'))).toBe(true);
      expect(logs.some(log => log.includes('首次启动') || log.includes('first-launch'))).toBe(true);
    }, { timeout: 10000 });
    
    consoleSpy.mockRestore();
  });

  test('ActivationGuard在严格启动流程期间不应拦截', async () => {
    // 设置严格启动流程未完成状态
    const { useStrictStartupFlowStore } = require('../stores/strictStartupFlowStore');
    
    // Mock store 返回未完成状态
    jest.mocked(useStrictStartupFlowStore).mockReturnValue({
      isCompleted: false,
      currentPhase: 'network-check',
      // ... 其他属性
    });

    render(<App />);
    
    // ActivationGuard 不应该显示激活检查界面
    await waitFor(() => {
      expect(screen.queryByText('正在验证激活状态')).not.toBeInTheDocument();
    }, { timeout: 2000 });
  });
});

// 集成测试：完整启动流程
describe('完整启动流程集成测试', () => {
  test('模拟完整的正常启动流程', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    
    render(<App />);
    
    // 1. 应用初始化
    expect(screen.getByText('正在加载玩机管家')).toBeInTheDocument();
    
    // 2. 等待进入严格启动流程
    await waitFor(() => {
      expect(screen.queryByText('正在加载玩机管家')).not.toBeInTheDocument();
    }, { timeout: 2000 });
    
    // 3. 网络检查阶段
    await waitFor(() => {
      const logs = consoleSpy.mock.calls.map(call => call.join(' '));
      expect(logs.some(log => log.includes('网络检查') || log.includes('network-check'))).toBe(true);
    }, { timeout: 3000 });
    
    // 4. 版本检查阶段
    await waitFor(() => {
      const logs = consoleSpy.mock.calls.map(call => call.join(' '));
      expect(logs.some(log => log.includes('版本检查') || log.includes('version-check'))).toBe(true);
    }, { timeout: 3000 });
    
    // 5. 首次启动检测阶段
    await waitFor(() => {
      const logs = consoleSpy.mock.calls.map(call => call.join(' '));
      expect(logs.some(log => log.includes('首次启动') || log.includes('first-launch'))).toBe(true);
    }, { timeout: 3000 });
    
    // 6. 最终应该完成启动流程
    await waitFor(() => {
      const logs = consoleSpy.mock.calls.map(call => call.join(' '));
      expect(logs.some(log => log.includes('启动流程完成') || log.includes('completed'))).toBe(true);
    }, { timeout: 5000 });
    
    consoleSpy.mockRestore();
  });
});

export default {};
