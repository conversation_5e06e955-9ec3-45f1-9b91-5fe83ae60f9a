/**
 * 增强激活验证服务
 * 添加设备绑定、防重放攻击和安全性增强
 */

import { invoke } from '@tauri-apps/api/core';
import { ActivationStatus, ActivationRequest, ActivationResponse } from '../types/welcome';
import { activationLogger } from './activationLogger';

// 设备指纹信息
export interface DeviceFingerprint {
  deviceId: string;
  platform: string;
  architecture: string;
  osVersion: string;
  cpuInfo: string;
  memoryInfo: string;
  diskInfo: string;
  networkInfo: string;
  timestamp: number;
  hash: string;
}

// 安全激活请求
export interface SecureActivationRequest extends ActivationRequest {
  deviceFingerprint: DeviceFingerprint;
  requestTimestamp: number;
  nonce: string;
  signature: string;
}

// 激活验证结果
export interface ActivationVerificationResult {
  isValid: boolean;
  isActivated: boolean;
  isExpired: boolean;
  expiryDate?: Date;
  deviceBound: boolean;
  securityLevel: 'high' | 'medium' | 'low';
  warnings: string[];
  errors: string[];
}

class EnhancedActivationService {
  private static instance: EnhancedActivationService;
  private readonly STORAGE_KEY = 'hout_secure_activation_data';
  private readonly DEVICE_KEY = 'hout_device_fingerprint';
  private readonly MAX_ACTIVATION_ATTEMPTS = 5;
  private readonly ACTIVATION_TIMEOUT = 30000; // 30秒

  public static getInstance(): EnhancedActivationService {
    if (!EnhancedActivationService.instance) {
      EnhancedActivationService.instance = new EnhancedActivationService();
    }
    return EnhancedActivationService.instance;
  }

  /**
   * 生成设备指纹
   */
  public async generateDeviceFingerprint(): Promise<DeviceFingerprint> {
    try {
      activationLogger.debug('DEVICE_FINGERPRINT', '开始生成设备指纹');

      // 获取系统信息
      const platform = await this.getPlatformInfo();
      const architecture = await this.getArchitectureInfo();
      const osVersion = await this.getOSVersion();
      const cpuInfo = await this.getCPUInfo();
      const memoryInfo = await this.getMemoryInfo();
      const diskInfo = await this.getDiskInfo();
      const networkInfo = await this.getNetworkInfo();

      const timestamp = Date.now();
      const deviceId = await this.generateDeviceId();

      // 生成指纹哈希
      const fingerprintData = {
        deviceId,
        platform,
        architecture,
        osVersion,
        cpuInfo,
        memoryInfo,
        diskInfo,
        networkInfo,
        timestamp,
      };

      const hash = await this.generateHash(JSON.stringify(fingerprintData));

      const fingerprint: DeviceFingerprint = {
        ...fingerprintData,
        hash,
      };

      activationLogger.debug('DEVICE_FINGERPRINT', '设备指纹生成完成', {
        deviceId: fingerprint.deviceId,
        platform: fingerprint.platform,
        hash: fingerprint.hash.substring(0, 8) + '...',
      });

      // 保存设备指纹
      this.saveDeviceFingerprint(fingerprint);

      return fingerprint;

    } catch (error) {
      activationLogger.error('DEVICE_FINGERPRINT', '生成设备指纹失败', error);
      throw new Error('设备指纹生成失败');
    }
  }

  /**
   * 验证设备指纹
   */
  public async verifyDeviceFingerprint(fingerprint: DeviceFingerprint): Promise<boolean> {
    try {
      const storedFingerprint = this.getStoredDeviceFingerprint();
      
      if (!storedFingerprint) {
        activationLogger.warn('DEVICE_FINGERPRINT', '未找到存储的设备指纹');
        return false;
      }

      // 验证关键字段
      const criticalFields = ['deviceId', 'platform', 'architecture', 'osVersion'];
      for (const field of criticalFields) {
        if (fingerprint[field as keyof DeviceFingerprint] !== storedFingerprint[field as keyof DeviceFingerprint]) {
          activationLogger.warn('DEVICE_FINGERPRINT', `设备指纹字段不匹配: ${field}`);
          return false;
        }
      }

      // 验证哈希
      const expectedHash = await this.generateHash(JSON.stringify({
        deviceId: fingerprint.deviceId,
        platform: fingerprint.platform,
        architecture: fingerprint.architecture,
        osVersion: fingerprint.osVersion,
        cpuInfo: fingerprint.cpuInfo,
        memoryInfo: fingerprint.memoryInfo,
        diskInfo: fingerprint.diskInfo,
        networkInfo: fingerprint.networkInfo,
        timestamp: fingerprint.timestamp,
      }));

      if (fingerprint.hash !== expectedHash) {
        activationLogger.warn('DEVICE_FINGERPRINT', '设备指纹哈希验证失败');
        return false;
      }

      activationLogger.debug('DEVICE_FINGERPRINT', '设备指纹验证成功');
      return true;

    } catch (error) {
      activationLogger.error('DEVICE_FINGERPRINT', '设备指纹验证失败', error);
      return false;
    }
  }

  /**
   * 安全激活验证
   */
  public async secureActivateApplication(activationCode: string): Promise<ActivationResponse> {
    try {
      activationLogger.info('SECURE_ACTIVATION', '开始安全激活验证', {
        codeLength: activationCode.length,
        timestamp: Date.now(),
      });

      // 1. 生成设备指纹
      const deviceFingerprint = await this.generateDeviceFingerprint();

      // 2. 生成随机数和时间戳
      const nonce = this.generateNonce();
      const requestTimestamp = Date.now();

      // 3. 创建安全请求
      const secureRequest: SecureActivationRequest = {
        activationCode: activationCode.trim(),
        userConfig: {
          username: 'HOUT用户',
          language: 'zh-CN',
          theme: 'light',
          autoStart: false,
          checkUpdates: true,
          enableTelemetry: false,
        },
        deviceInfo: {
          platform: deviceFingerprint.platform,
          version: '1.0.0',
          deviceId: deviceFingerprint.deviceId,
        },
        deviceFingerprint,
        requestTimestamp,
        nonce,
        signature: await this.generateRequestSignature(activationCode, deviceFingerprint, nonce, requestTimestamp),
      };

      // 4. 调用后端验证
      const response = await invoke<ActivationResponse>('secure_activate_application', {
        request: secureRequest
      });

      // 5. 验证响应
      if (response.success) {
        activationLogger.info('SECURE_ACTIVATION', '安全激活验证成功');
        
        // 保存激活信息
        await this.saveSecureActivationData(response, deviceFingerprint);
        
        return response;
      } else {
        activationLogger.warn('SECURE_ACTIVATION', '安全激活验证失败', {
          status: response.status,
          message: response.message,
        });
        
        return response;
      }

    } catch (error) {
      activationLogger.error('SECURE_ACTIVATION', '安全激活验证异常', error);
      
      return {
        success: false,
        status: ActivationStatus.ActivationFailed,
        message: error instanceof Error ? error.message : '激活验证失败',
      };
    }
  }

  /**
   * 验证当前激活状态
   */
  public async verifyCurrentActivation(): Promise<ActivationVerificationResult> {
    try {
      const activationData = this.getSecureActivationData();
      const deviceFingerprint = this.getStoredDeviceFingerprint();
      
      const result: ActivationVerificationResult = {
        isValid: false,
        isActivated: false,
        isExpired: false,
        deviceBound: false,
        securityLevel: 'low',
        warnings: [],
        errors: [],
      };

      // 检查激活数据
      if (!activationData) {
        result.errors.push('未找到激活数据');
        return result;
      }

      // 检查设备绑定
      if (!deviceFingerprint) {
        result.errors.push('未找到设备指纹');
        return result;
      }

      const currentFingerprint = await this.generateDeviceFingerprint();
      const deviceMatches = await this.verifyDeviceFingerprint(currentFingerprint);
      
      if (!deviceMatches) {
        result.errors.push('设备指纹不匹配');
        return result;
      }

      result.deviceBound = true;

      // 检查激活状态
      if (activationData.isActivated) {
        result.isActivated = true;

        // 检查过期时间
        if (activationData.expiresAt) {
          const expiryDate = new Date(activationData.expiresAt);
          const now = new Date();
          
          result.expiryDate = expiryDate;
          
          if (now > expiryDate) {
            result.isExpired = true;
            result.errors.push('激活已过期');
          } else {
            result.isValid = true;
            
            // 计算安全级别
            const daysUntilExpiry = Math.floor((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
            
            if (daysUntilExpiry > 30) {
              result.securityLevel = 'high';
            } else if (daysUntilExpiry > 7) {
              result.securityLevel = 'medium';
              result.warnings.push(`激活将在 ${daysUntilExpiry} 天后过期`);
            } else {
              result.securityLevel = 'low';
              result.warnings.push(`激活即将过期（${daysUntilExpiry} 天）`);
            }
          }
        } else {
          result.errors.push('激活数据缺少过期时间');
        }
      } else {
        result.errors.push('激活状态无效');
      }

      activationLogger.debug('ACTIVATION_VERIFICATION', '激活状态验证完成', {
        isValid: result.isValid,
        isActivated: result.isActivated,
        isExpired: result.isExpired,
        securityLevel: result.securityLevel,
        warningCount: result.warnings.length,
        errorCount: result.errors.length,
      });

      return result;

    } catch (error) {
      activationLogger.error('ACTIVATION_VERIFICATION', '激活状态验证失败', error);
      
      return {
        isValid: false,
        isActivated: false,
        isExpired: false,
        deviceBound: false,
        securityLevel: 'low',
        warnings: [],
        errors: [error instanceof Error ? error.message : '验证失败'],
      };
    }
  }

  // 私有方法实现...
  private async getPlatformInfo(): Promise<string> {
    try {
      const { platform } = await import('@tauri-apps/api/os');
      return await platform();
    } catch {
      return 'unknown';
    }
  }

  private async getArchitectureInfo(): Promise<string> {
    try {
      const { arch } = await import('@tauri-apps/api/os');
      return await arch();
    } catch {
      return 'unknown';
    }
  }

  private async getOSVersion(): Promise<string> {
    try {
      const { version } = await import('@tauri-apps/api/os');
      return await version();
    } catch {
      return 'unknown';
    }
  }

  private async getCPUInfo(): Promise<string> {
    // 简化的CPU信息获取
    return `cores:${navigator.hardwareConcurrency || 'unknown'}`;
  }

  private async getMemoryInfo(): Promise<string> {
    // 简化的内存信息获取
    return `memory:${(navigator as any).deviceMemory || 'unknown'}GB`;
  }

  private async getDiskInfo(): Promise<string> {
    // 简化的磁盘信息获取
    return 'disk:unknown';
  }

  private async getNetworkInfo(): Promise<string> {
    // 简化的网络信息获取
    const connection = (navigator as any).connection;
    return connection ? `type:${connection.effectiveType}` : 'network:unknown';
  }

  private async generateDeviceId(): Promise<string> {
    // 生成基于硬件信息的设备ID
    const info = [
      navigator.userAgent,
      navigator.language,
      screen.width,
      screen.height,
      new Date().getTimezoneOffset(),
    ].join('|');
    
    return await this.generateHash(info);
  }

  private async generateHash(data: string): Promise<string> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private generateNonce(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  private async generateRequestSignature(
    activationCode: string,
    deviceFingerprint: DeviceFingerprint,
    nonce: string,
    timestamp: number
  ): Promise<string> {
    const signatureData = [
      activationCode,
      deviceFingerprint.hash,
      nonce,
      timestamp.toString(),
    ].join('|');
    
    return await this.generateHash(signatureData);
  }

  private saveDeviceFingerprint(fingerprint: DeviceFingerprint): void {
    try {
      localStorage.setItem(this.DEVICE_KEY, JSON.stringify(fingerprint));
    } catch (error) {
      activationLogger.error('STORAGE', '保存设备指纹失败', error);
    }
  }

  private getStoredDeviceFingerprint(): DeviceFingerprint | null {
    try {
      const stored = localStorage.getItem(this.DEVICE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      activationLogger.error('STORAGE', '读取设备指纹失败', error);
      return null;
    }
  }

  private async saveSecureActivationData(response: ActivationResponse, deviceFingerprint: DeviceFingerprint): Promise<void> {
    try {
      const secureData = {
        ...response,
        deviceFingerprint,
        savedAt: new Date().toISOString(),
      };
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(secureData));
      activationLogger.debug('STORAGE', '安全激活数据已保存');
    } catch (error) {
      activationLogger.error('STORAGE', '保存安全激活数据失败', error);
    }
  }

  private getSecureActivationData(): any {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      activationLogger.error('STORAGE', '读取安全激活数据失败', error);
      return null;
    }
  }
}

export const enhancedActivationService = EnhancedActivationService.getInstance();
export default enhancedActivationService;
