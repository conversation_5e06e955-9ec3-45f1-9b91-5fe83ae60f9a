/**
 * 启动流程进度指示器
 * 显示当前启动进度、阶段信息和用户引导
 */

import React from 'react';
import {
  makeStyles,
  Text,
  Title3,
  ProgressBar,
  Card,
  Badge,
  tokens,
} from '@fluentui/react-components';
import {
  Wifi24Regular,
  Shield24Regular,
  PersonAccounts24Regular,
  Key24Regular,
  Home24Regular,
  Database24Regular,
  Checkmark24Regular,
  Warning24Regular,
  Spinner,
} from '@fluentui/react-icons';
import { StrictStartupPhase } from '../../stores/strictStartupFlowStore';

const useStyles = makeStyles({
  container: {
    position: 'fixed',
    top: tokens.spacingVerticalL,
    right: tokens.spacingHorizontalL,
    width: '300px',
    zIndex: 1000,
  },
  card: {
    padding: tokens.spacingVerticalM,
    backgroundColor: tokens.colorNeutralBackground1,
    border: `1px solid ${tokens.colorNeutralStroke1}`,
    boxShadow: tokens.shadow8,
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
  },
  title: {
    marginBottom: tokens.spacingVerticalS,
  },
  progressContainer: {
    marginBottom: tokens.spacingVerticalM,
  },
  progressText: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: tokens.spacingVerticalXS,
    fontSize: tokens.fontSizeBase200,
  },
  phaseList: {
    display: 'flex',
    flexDirection: 'column',
    gap: tokens.spacingVerticalXS,
  },
  phaseItem: {
    display: 'flex',
    alignItems: 'center',
    gap: tokens.spacingHorizontalS,
    padding: tokens.spacingVerticalXS,
    borderRadius: tokens.borderRadiusSmall,
    transition: 'background-color 0.2s ease',
  },
  phaseItemCurrent: {
    backgroundColor: tokens.colorBrandBackground2,
  },
  phaseItemCompleted: {
    backgroundColor: tokens.colorPaletteGreenBackground1,
  },
  phaseItemFailed: {
    backgroundColor: tokens.colorPaletteRedBackground1,
  },
  phaseIcon: {
    fontSize: '16px',
    minWidth: '16px',
  },
  phaseText: {
    flex: 1,
    fontSize: tokens.fontSizeBase200,
  },
  phaseBadge: {
    fontSize: tokens.fontSizeBase100,
  },
  currentPhaseInfo: {
    marginTop: tokens.spacingVerticalM,
    padding: tokens.spacingVerticalS,
    backgroundColor: tokens.colorNeutralBackground2,
    borderRadius: tokens.borderRadiusSmall,
  },
  currentPhaseTitle: {
    fontWeight: tokens.fontWeightSemibold,
    marginBottom: tokens.spacingVerticalXS,
  },
  currentPhaseDescription: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
  },
});

interface StartupProgressIndicatorProps {
  currentPhase: StrictStartupPhase;
  phaseStatuses: Record<StrictStartupPhase, any>;
  progress: number;
  isVisible?: boolean;
}

// 阶段配置
const PHASE_CONFIG: Record<StrictStartupPhase, {
  icon: React.ReactElement;
  title: string;
  description: string;
  userGuidance: string;
}> = {
  'network-check': {
    icon: <Wifi24Regular />,
    title: '网络检查',
    description: '检查网络连接状态',
    userGuidance: '正在检查网络连接，请确保设备已连接到互联网',
  },
  'version-check': {
    icon: <Shield24Regular />,
    title: '版本检查',
    description: '检查应用版本和安全更新',
    userGuidance: '正在验证应用版本，确保您使用的是最新版本',
  },
  'first-launch-detection': {
    icon: <PersonAccounts24Regular />,
    title: '启动检测',
    description: '检测首次启动状态',
    userGuidance: '正在检测应用启动状态，准备个性化设置',
  },
  'privacy-consent': {
    icon: <PersonAccounts24Regular />,
    title: '隐私政策',
    description: '确认隐私政策和用户协议',
    userGuidance: '请仔细阅读并同意隐私政策，这是使用应用的必要条件',
  },
  'activation-verification': {
    icon: <Key24Regular />,
    title: '激活验证',
    description: '验证激活码',
    userGuidance: '请输入有效的激活码以继续使用应用',
  },
  'main-app': {
    icon: <Home24Regular />,
    title: '加载主界面',
    description: '初始化主应用界面',
    userGuidance: '正在加载主界面，马上就可以开始使用了',
  },
  'data-collection': {
    icon: <Database24Regular />,
    title: '数据收集',
    description: '初始化数据收集服务',
    userGuidance: '正在初始化数据收集服务，用于改善用户体验',
  },
  'completed': {
    icon: <Checkmark24Regular />,
    title: '启动完成',
    description: '应用启动流程已完成',
    userGuidance: '启动流程已完成，欢迎使用玩机管家！',
  },
  'error': {
    icon: <Warning24Regular />,
    title: '启动错误',
    description: '启动过程中发生错误',
    userGuidance: '启动过程中遇到问题，请检查网络连接或联系技术支持',
  },
};

const StartupProgressIndicator: React.FC<StartupProgressIndicatorProps> = ({
  currentPhase,
  phaseStatuses,
  progress,
  isVisible = true,
}) => {
  const styles = useStyles();

  if (!isVisible) {
    return null;
  }

  // 获取阶段状态图标
  const getPhaseStatusIcon = (phase: StrictStartupPhase) => {
    const status = phaseStatuses[phase]?.status;
    
    switch (status) {
      case 'completed':
        return <Checkmark24Regular style={{ color: tokens.colorPaletteGreenForeground1 }} />;
      case 'failed':
        return <Warning24Regular style={{ color: tokens.colorPaletteRedForeground1 }} />;
      case 'running':
        return <Spinner size="extra-small" />;
      default:
        return PHASE_CONFIG[phase].icon;
    }
  };

  // 获取阶段状态样式
  const getPhaseItemStyle = (phase: StrictStartupPhase) => {
    const status = phaseStatuses[phase]?.status;
    const isCurrent = phase === currentPhase;
    
    if (status === 'completed') {
      return `${styles.phaseItem} ${styles.phaseItemCompleted}`;
    } else if (status === 'failed') {
      return `${styles.phaseItem} ${styles.phaseItemFailed}`;
    } else if (isCurrent) {
      return `${styles.phaseItem} ${styles.phaseItemCurrent}`;
    } else {
      return styles.phaseItem;
    }
  };

  // 获取阶段状态徽章
  const getPhaseStatusBadge = (phase: StrictStartupPhase) => {
    const status = phaseStatuses[phase]?.status;
    const retryCount = phaseStatuses[phase]?.retryCount || 0;
    
    switch (status) {
      case 'completed':
        return <Badge appearance="filled" color="success" className={styles.phaseBadge}>完成</Badge>;
      case 'failed':
        return <Badge appearance="filled" color="danger" className={styles.phaseBadge}>失败</Badge>;
      case 'running':
        return retryCount > 0 
          ? <Badge appearance="filled" color="warning" className={styles.phaseBadge}>重试{retryCount}</Badge>
          : <Badge appearance="filled" color="brand" className={styles.phaseBadge}>进行中</Badge>;
      default:
        return <Badge appearance="outline" className={styles.phaseBadge}>等待</Badge>;
    }
  };

  const currentPhaseConfig = PHASE_CONFIG[currentPhase];
  const completedCount = Object.values(phaseStatuses).filter(status => status.status === 'completed').length;
  const totalCount = Object.keys(phaseStatuses).length - 1; // 排除error阶段

  return (
    <div className={styles.container}>
      <Card className={styles.card}>
        <div className={styles.header}>
          <Title3 className={styles.title}>启动进度</Title3>
          
          <div className={styles.progressContainer}>
            <div className={styles.progressText}>
              <Text>进度</Text>
              <Text>{Math.round(progress)}%</Text>
            </div>
            <ProgressBar value={progress / 100} />
            <div className={styles.progressText}>
              <Text>{completedCount} / {totalCount} 已完成</Text>
            </div>
          </div>
        </div>

        <div className={styles.phaseList}>
          {Object.entries(PHASE_CONFIG).map(([phase, config]) => {
            if (phase === 'error') return null; // 不显示错误阶段
            
            return (
              <div
                key={phase}
                className={getPhaseItemStyle(phase as StrictStartupPhase)}
              >
                <div className={styles.phaseIcon}>
                  {getPhaseStatusIcon(phase as StrictStartupPhase)}
                </div>
                <div className={styles.phaseText}>
                  <Text>{config.title}</Text>
                </div>
                {getPhaseStatusBadge(phase as StrictStartupPhase)}
              </div>
            );
          })}
        </div>

        {currentPhase !== 'completed' && currentPhase !== 'error' && (
          <div className={styles.currentPhaseInfo}>
            <div className={styles.currentPhaseTitle}>
              <Text>{currentPhaseConfig.title}</Text>
            </div>
            <div className={styles.currentPhaseDescription}>
              <Text>{currentPhaseConfig.userGuidance}</Text>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default StartupProgressIndicator;

// 导出阶段配置供其他组件使用
export { PHASE_CONFIG };
