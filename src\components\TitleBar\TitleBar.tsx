import React from "react";
import {
  makeSty<PERSON>,
  <PERSON><PERSON>,
  Text,
  Tooltip,
} from "@fluentui/react-components";
import {
  WeatherMoon24Regular,
  WeatherSunny24Regular,
  Settings24Regular,
  Subtract24Regular,
  Dismiss24Regular,
  Maximize24Regular,
  SquareMultiple24Regular,
  Person24Regular,
  Pin24Regular,
  PinOff24Regular,
} from "@fluentui/react-icons";
import { useThemeStore } from "../../stores/themeStore";
import { useAppStore } from "../../stores/appStore";
import { useAppConfigStore } from "../../stores/welcomeStore";
import { getCurrentWindow } from "@tauri-apps/api/window";
import { invoke } from "@tauri-apps/api/core";
import UserInfoModal from "../UserInfo/UserInfoModal";
import AppIcon from "../Common/AppIcon";

const useStyles = makeStyles({
  titleBar: {
    height: "48px",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "var(--colorNeutralBackground1)",
    borderBottom: "1px solid var(--colorNeutralStroke2)",
    paddingLeft: "16px",
    paddingRight: "8px",
  },
  leftSection: {
    display: "flex",
    alignItems: "center",
    gap: "12px",
  },
  logo: {
    width: "24px",
    height: "24px",
  },
  title: {
    fontWeight: "600",
    fontSize: "14px",
    color: "var(--colorNeutralForeground1)",
  },
  rightSection: {
    display: "flex",
    alignItems: "center",
    gap: "4px",
  },
  titleBarButton: {
    minWidth: "32px",
    height: "32px",
    borderRadius: "4px",
  },
  closeButton: {
    ":hover": {
      backgroundColor: "#E81123 !important",
      color: "white !important",
    },
  },
});

const TitleBar: React.FC = () => {
  const styles = useStyles();
  const { isDarkMode, toggleTheme } = useThemeStore();
  const { setCurrentView } = useAppStore();
  const { config } = useAppConfigStore();
  const [isMaximized, setIsMaximized] = React.useState(false);
  const [isAlwaysOnTop, setIsAlwaysOnTop] = React.useState(false);
  const [isAlwaysOnTopInitialized, setIsAlwaysOnTopInitialized] = React.useState(false);

  // 检查窗口状态 - 添加防抖和缓存
  React.useEffect(() => {
    let isChecking = false;
    let checkTimeout: number | null = null;

    const checkWindowState = async () => {
      if (isChecking) {
        console.log("TitleBar: 跳过重复的窗口状态检查");
        return;
      }

      isChecking = true;
      try {
        const window = getCurrentWindow();
        const maximized = await window.isMaximized();
        console.log("TitleBar: 窗口状态检查:", maximized);
        setIsMaximized(maximized);

        // 只在初始化时检查置顶状态，避免频繁调用
        if (!isAlwaysOnTopInitialized) {
          try {
            const alwaysOnTop = await invoke<boolean>("get_window_always_on_top");
            setIsAlwaysOnTop(alwaysOnTop);
            setIsAlwaysOnTopInitialized(true);
            console.log("TitleBar: 初始化窗口置顶状态:", alwaysOnTop);
          } catch (alwaysOnTopError) {
            console.log("TitleBar: 无法获取置顶状态，使用手动管理:", alwaysOnTopError);
            setIsAlwaysOnTopInitialized(true);
          }
        }
      } catch (error) {
        console.error("TitleBar: 检查窗口状态失败:", error);
      } finally {
        isChecking = false;
      }
    };

    // 防抖的窗口状态检查
    const debouncedCheckWindowState = () => {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }
      checkTimeout = setTimeout(checkWindowState, 300);
    };

    // 初始检查
    checkWindowState();

    // 监听窗口状态变化 - 使用防抖
    let unlistenPromise: Promise<() => void> | null = null;

    const setupListener = async () => {
      try {
        const window = getCurrentWindow();
        unlistenPromise = window.onResized(() => {
          debouncedCheckWindowState();
        });
      } catch (error) {
        console.error("TitleBar: 设置窗口监听器失败:", error);
      }
    };

    setupListener();

    return () => {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }
      if (unlistenPromise) {
        unlistenPromise.then(fn => fn()).catch(console.error);
      }
    };
  }, []);

  const handleMinimize = async () => {
    try {
      console.log("🔧 执行窗口最小化...");
      const window = getCurrentWindow();
      await window.minimize();
      console.log("✅ 窗口最小化成功");
    } catch (error) {
      console.error("❌ 最小化失败:", error);
    }
  };

  const handleMaximize = async () => {
    try {
      const window = getCurrentWindow();
      if (isMaximized) {
        console.log("🔧 执行窗口还原...");
        await window.unmaximize();
        console.log("✅ 窗口还原成功");
      } else {
        console.log("🔧 执行窗口最大化...");
        await window.maximize();
        console.log("✅ 窗口最大化成功");
      }
      setIsMaximized(!isMaximized);
    } catch (error) {
      console.error("❌ 最大化/还原失败:", error);
    }
  };

  const handleClose = async () => {
    try {
      console.log("🔧 执行窗口关闭...");
      const window = getCurrentWindow();
      await window.close();
      console.log("✅ 窗口关闭成功");
    } catch (error) {
      console.error("❌ 关闭失败:", error);
    }
  };

  const handleSettings = () => {
    setCurrentView("settings");
  };

  const handleToggleAlwaysOnTop = async () => {
    try {
      console.log("🔧 切换窗口置顶状态...");
      const newState = !isAlwaysOnTop;
      await invoke("set_window_always_on_top", { alwaysOnTop: newState });
      setIsAlwaysOnTop(newState);
      console.log(`✅ 窗口置顶状态已${newState ? '开启' : '关闭'}`);
    } catch (error) {
      console.error("❌ 切换置顶状态失败:", error);
    }
  };

  return (
    <div className={`${styles.titleBar} drag-region`}>
      <div className={styles.leftSection}>
        <div className={styles.logo}>
          <AppIcon size="medium"/>
        </div>
        <Text className={styles.title}>玩机管家-Android Device Management Tool</Text>
      </div>
      
      <div className={`${styles.rightSection} no-drag`}>
        {/* 用户信息按钮 - 始终显示 */}
        <UserInfoModal>
          <Tooltip content="我的信息" relationship="label">
            <Button
              appearance="subtle"
              icon={<Person24Regular />}
              className={styles.titleBarButton}
            />
          </Tooltip>
        </UserInfoModal>

        <Tooltip content="切换主题" relationship="label">
          <Button
            appearance="subtle"
            icon={isDarkMode ? <WeatherSunny24Regular /> : <WeatherMoon24Regular />}
            className={styles.titleBarButton}
            onClick={toggleTheme}
          />
        </Tooltip>

        <Tooltip content="设置" relationship="label">
          <Button
            appearance="subtle"
            icon={<Settings24Regular />}
            className={styles.titleBarButton}
            onClick={handleSettings}
          />
        </Tooltip>

        <Tooltip content={isAlwaysOnTop ? "取消置顶" : "窗口置顶"} relationship="label">
          <Button
            appearance="subtle"
            icon={isAlwaysOnTop ? <PinOff24Regular /> : <Pin24Regular />}
            className={styles.titleBarButton}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleToggleAlwaysOnTop();
            }}
          />
        </Tooltip>

        <Tooltip content="最小化" relationship="label">
          <Button
            appearance="subtle"
            icon={<Subtract24Regular />}
            className={styles.titleBarButton}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleMinimize();
            }}
          />
        </Tooltip>

        <Tooltip content={isMaximized ? "还原" : "最大化"} relationship="label">
          <Button
            appearance="subtle"
            icon={isMaximized ? <SquareMultiple24Regular /> : <Maximize24Regular />}
            className={styles.titleBarButton}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleMaximize();
            }}
          />
        </Tooltip>

        <Tooltip content="关闭" relationship="label">
          <Button
            appearance="subtle"
            icon={<Dismiss24Regular />}
            className={`${styles.titleBarButton} ${styles.closeButton}`}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleClose();
            }}
          />
        </Tooltip>
      </div>
    </div>
  );
};

export default TitleBar;
