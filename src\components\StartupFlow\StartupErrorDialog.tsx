/**
 * 启动流程错误对话框
 * 提供用户友好的错误提示和解决方案
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogSurface,
  DialogBody,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Text,
  Title2,
  MessageBar,
  MessageBarBody,
  Accordion,
  AccordionItem,
  AccordionHeader,
  AccordionPanel,
  makeStyles,
  tokens,
} from '@fluentui/react-components';
import {
  Warning24Regular,
  Dismiss24Regular,
  ArrowClockwise24Regular,
  Info24Regular,
  Settings24Regular,
  QuestionCircle24Regular,
} from '@fluentui/react-icons';
import { StrictStartupPhase } from '../../stores/strictStartupFlowStore';
import { PHASE_CONFIG } from './StartupProgressIndicator';

const useStyles = makeStyles({
  dialog: {
    maxWidth: '600px',
    width: '90vw',
  },
  errorIcon: {
    fontSize: '48px',
    color: tokens.colorPaletteRedForeground1,
    marginBottom: tokens.spacingVerticalM,
    textAlign: 'center',
  },
  errorTitle: {
    textAlign: 'center',
    marginBottom: tokens.spacingVerticalM,
    color: tokens.colorPaletteRedForeground1,
  },
  errorMessage: {
    marginBottom: tokens.spacingVerticalL,
    textAlign: 'center',
  },
  solutionSection: {
    marginBottom: tokens.spacingVerticalL,
  },
  solutionList: {
    paddingLeft: tokens.spacingHorizontalL,
    marginTop: tokens.spacingVerticalS,
  },
  solutionItem: {
    marginBottom: tokens.spacingVerticalS,
    display: 'flex',
    alignItems: 'flex-start',
    gap: tokens.spacingHorizontalS,
  },
  solutionNumber: {
    minWidth: '20px',
    height: '20px',
    borderRadius: '50%',
    backgroundColor: tokens.colorBrandBackground,
    color: tokens.colorNeutralForegroundOnBrand,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: tokens.fontSizeBase200,
    fontWeight: tokens.fontWeightSemibold,
  },
  solutionText: {
    flex: 1,
  },
  technicalDetails: {
    marginTop: tokens.spacingVerticalL,
  },
  codeBlock: {
    backgroundColor: tokens.colorNeutralBackground2,
    padding: tokens.spacingVerticalS,
    borderRadius: tokens.borderRadiusSmall,
    fontFamily: 'monospace',
    fontSize: tokens.fontSizeBase200,
    marginTop: tokens.spacingVerticalS,
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
  },
  actions: {
    display: 'flex',
    gap: tokens.spacingHorizontalM,
    justifyContent: 'flex-end',
  },
  warningBox: {
    marginBottom: tokens.spacingVerticalL,
  },
});

interface StartupErrorDialogProps {
  open: boolean;
  phase: StrictStartupPhase;
  error: string;
  canRetry?: boolean;
  canRecover?: boolean;
  onRetry?: () => void;
  onRecover?: () => void;
  onExit: () => void;
  onClose?: () => void;
}

// 错误解决方案配置
const ERROR_SOLUTIONS: Record<StrictStartupPhase, {
  title: string;
  description: string;
  solutions: string[];
  technicalInfo?: string;
  isRecoverable: boolean;
}> = {
  'network-check': {
    title: '网络连接失败',
    description: '应用无法连接到网络或API服务器，这是应用正常运行的必要条件。',
    solutions: [
      '检查您的网络连接是否正常',
      '确认防火墙或安全软件没有阻止应用访问网络',
      '尝试切换到其他网络（如移动热点）',
      '检查代理设置是否正确',
      '联系网络管理员确认网络策略',
    ],
    technicalInfo: '应用需要访问 https://api-g.lacs.cc 来验证版本和激活状态',
    isRecoverable: false,
  },
  'version-check': {
    title: '版本检查失败',
    description: '无法验证应用版本或获取更新信息，这可能影响应用的安全性和功能。',
    solutions: [
      '检查网络连接是否稳定',
      '确认系统时间设置正确',
      '尝试重新启动应用',
      '检查是否有安全软件阻止了网络访问',
      '联系技术支持获取最新版本',
    ],
    technicalInfo: '版本检查失败可能导致安全风险，应用将无法继续运行',
    isRecoverable: false,
  },
  'first-launch-detection': {
    title: '启动检测失败',
    description: '无法检测应用的启动状态，这可能影响个性化设置。',
    solutions: [
      '清除浏览器缓存和本地存储',
      '检查磁盘空间是否充足',
      '确认应用有足够的权限访问本地存储',
      '尝试以管理员权限运行应用',
    ],
    isRecoverable: true,
  },
  'privacy-consent': {
    title: '隐私政策确认失败',
    description: '隐私政策确认过程中出现问题，这是使用应用的必要步骤。',
    solutions: [
      '重新阅读并同意隐私政策',
      '确认您已满足使用条件',
      '检查网络连接是否正常',
      '清除浏览器缓存后重试',
    ],
    isRecoverable: true,
  },
  'activation-verification': {
    title: '激活验证失败',
    description: '激活码验证失败，无法继续使用应用。',
    solutions: [
      '检查激活码是否输入正确（注意大小写）',
      '确认激活码未过期且未被使用',
      '检查网络连接是否正常',
      '联系客服获取新的激活码',
      '确认设备符合激活要求',
    ],
    technicalInfo: '激活码验证失败可能是由于网络问题、激活码无效或设备不匹配',
    isRecoverable: true,
  },
  'main-app': {
    title: '主界面加载失败',
    description: '主应用界面加载过程中出现问题。',
    solutions: [
      '检查系统资源是否充足',
      '关闭其他占用资源的应用',
      '重新启动应用',
      '检查应用文件是否完整',
    ],
    isRecoverable: true,
  },
  'data-collection': {
    title: '数据收集初始化失败',
    description: '数据收集服务初始化失败，但不影响主要功能。',
    solutions: [
      '检查隐私设置是否正确',
      '确认网络连接正常',
      '重新启动应用',
      '在设置中重新配置数据收集选项',
    ],
    isRecoverable: true,
  },
  'completed': {
    title: '启动完成',
    description: '启动流程已成功完成。',
    solutions: [],
    isRecoverable: false,
  },
  'error': {
    title: '未知错误',
    description: '启动过程中发生了未知错误。',
    solutions: [
      '重新启动应用',
      '检查系统要求是否满足',
      '联系技术支持',
      '查看应用日志获取更多信息',
    ],
    isRecoverable: true,
  },
};

const StartupErrorDialog: React.FC<StartupErrorDialogProps> = ({
  open,
  phase,
  error,
  canRetry = false,
  canRecover = false,
  onRetry,
  onRecover,
  onExit,
  onClose,
}) => {
  const styles = useStyles();
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(false);

  const errorConfig = ERROR_SOLUTIONS[phase];
  const phaseConfig = PHASE_CONFIG[phase];

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
    if (onClose) {
      onClose();
    }
  };

  const handleRecover = () => {
    if (onRecover) {
      onRecover();
    }
    if (onClose) {
      onClose();
    }
  };

  const handleExit = () => {
    onExit();
  };

  return (
    <Dialog open={open} modalType="modal">
      <DialogSurface className={styles.dialog}>
        <DialogBody>
          <DialogTitle>
            <div className={styles.errorIcon}>
              <Warning24Regular />
            </div>
            <Title2 className={styles.errorTitle}>
              {errorConfig.title}
            </Title2>
          </DialogTitle>

          <DialogContent>
            <div className={styles.errorMessage}>
              <Text>{errorConfig.description}</Text>
            </div>

            {error && (
              <MessageBar intent="error" className={styles.warningBox}>
                <MessageBarBody>
                  <strong>错误详情：</strong>{error}
                </MessageBarBody>
              </MessageBar>
            )}

            {!errorConfig.isRecoverable && (
              <MessageBar intent="warning" className={styles.warningBox}>
                <MessageBarBody>
                  <Warning24Regular /> 此错误无法恢复，应用将退出。请解决问题后重新启动应用。
                </MessageBarBody>
              </MessageBar>
            )}

            {errorConfig.solutions.length > 0 && (
              <div className={styles.solutionSection}>
                <Text weight="semibold">
                  <Settings24Regular /> 解决方案：
                </Text>
                <div className={styles.solutionList}>
                  {errorConfig.solutions.map((solution, index) => (
                    <div key={index} className={styles.solutionItem}>
                      <div className={styles.solutionNumber}>
                        {index + 1}
                      </div>
                      <div className={styles.solutionText}>
                        <Text>{solution}</Text>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {errorConfig.technicalInfo && (
              <div className={styles.technicalDetails}>
                <Accordion>
                  <AccordionItem value="technical">
                    <AccordionHeader>
                      <Info24Regular /> 技术详情
                    </AccordionHeader>
                    <AccordionPanel>
                      <Text>{errorConfig.technicalInfo}</Text>
                      {error && (
                        <div className={styles.codeBlock}>
                          错误信息: {error}
                          {'\n'}阶段: {phase}
                          {'\n'}时间: {new Date().toLocaleString()}
                        </div>
                      )}
                    </AccordionPanel>
                  </AccordionItem>
                </Accordion>
              </div>
            )}
          </DialogContent>

          <DialogActions className={styles.actions}>
            {canRetry && onRetry && (
              <Button
                appearance="primary"
                icon={<ArrowClockwise24Regular />}
                onClick={handleRetry}
              >
                重试
              </Button>
            )}
            
            {canRecover && onRecover && (
              <Button
                appearance="secondary"
                icon={<QuestionCircle24Regular />}
                onClick={handleRecover}
              >
                尝试恢复
              </Button>
            )}

            <Button
              appearance="secondary"
              icon={<Dismiss24Regular />}
              onClick={handleExit}
            >
              退出应用
            </Button>
          </DialogActions>
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
};

export default StartupErrorDialog;
