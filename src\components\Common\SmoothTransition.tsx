/**
 * 平滑过渡组件
 * 提供平滑的进入和退出动画，防止UI抖动
 */

import React, { useState, useEffect, useRef } from 'react';
import { makeStyles } from '@fluentui/react-components';

const useStyles = makeStyles({
  container: {
    transition: 'all 0.3s ease-in-out',
    willChange: 'opacity, transform',
  },
  entering: {
    opacity: 0,
    transform: 'translateY(10px) scale(0.98)',
  },
  entered: {
    opacity: 1,
    transform: 'translateY(0) scale(1)',
  },
  exiting: {
    opacity: 0,
    transform: 'translateY(-10px) scale(0.98)',
  },
  exited: {
    opacity: 0,
    transform: 'translateY(-10px) scale(0.98)',
    pointerEvents: 'none',
  },
});

interface SmoothTransitionProps {
  children: React.ReactNode;
  show: boolean;
  duration?: number;
  delay?: number;
  className?: string;
  onEntered?: () => void;
  onExited?: () => void;
}

type TransitionState = 'entering' | 'entered' | 'exiting' | 'exited';

const SmoothTransition: React.FC<SmoothTransitionProps> = ({
  children,
  show,
  duration = 300,
  delay = 0,
  className = '',
  onEntered,
  onExited,
}) => {
  const styles = useStyles();
  const [state, setState] = useState<TransitionState>(show ? 'entered' : 'exited');
  const [shouldRender, setShouldRender] = useState(show);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (show) {
      // 显示动画
      setShouldRender(true);
      
      timeoutRef.current = setTimeout(() => {
        if (mountedRef.current) {
          setState('entering');
          
          timeoutRef.current = setTimeout(() => {
            if (mountedRef.current) {
              setState('entered');
              onEntered?.();
            }
          }, delay + 50);
        }
      }, 10);
    } else {
      // 隐藏动画
      setState('exiting');
      
      timeoutRef.current = setTimeout(() => {
        if (mountedRef.current) {
          setState('exited');
          setShouldRender(false);
          onExited?.();
        }
      }, duration);
    }
  }, [show, duration, delay, onEntered, onExited]);

  if (!shouldRender) {
    return null;
  }

  const combinedClassName = `${styles.container} ${styles[state]} ${className}`.trim();

  return (
    <div className={combinedClassName} style={{ transitionDuration: `${duration}ms` }}>
      {children}
    </div>
  );
};

export default SmoothTransition;
