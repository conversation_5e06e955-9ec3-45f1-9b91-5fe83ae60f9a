/**
 * 错误边界组件
 * 捕获应用中的JavaScript错误，防止应用崩溃
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  makeStyles,
  Text,
  Button,
  MessageBar,
  MessageBarBody,
} from '@fluentui/react-components';
import {
  ErrorCircle24Regular,
  ArrowClockwise24Regular,
  Home24Regular,
} from '@fluentui/react-icons';

const useStyles = makeStyles({
  container: {
    width: '100%',
    height: '100vh',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '24px',
    padding: '40px',
    backgroundColor: 'var(--colorNeutralBackground1)',
    background: 'linear-gradient(135deg, var(--colorNeutralBackground1) 0%, var(--colorNeutralBackground2) 100%)',
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '16px',
    maxWidth: '600px',
    textAlign: 'center',
  },
  icon: {
    fontSize: '64px',
    color: 'var(--colorPaletteRedForeground1)',
  },
  title: {
    fontSize: '24px',
    fontWeight: '600',
    color: 'var(--colorNeutralForeground1)',
  },
  description: {
    fontSize: '16px',
    color: 'var(--colorNeutralForeground2)',
    lineHeight: '1.5',
  },
  errorDetails: {
    width: '100%',
    maxWidth: '500px',
    padding: '16px',
    backgroundColor: 'var(--colorNeutralBackground2)',
    borderRadius: '8px',
    border: '1px solid var(--colorNeutralStroke2)',
    fontFamily: 'monospace',
    fontSize: '12px',
    color: 'var(--colorNeutralForeground2)',
    textAlign: 'left',
    overflow: 'auto',
    maxHeight: '200px',
  },
  actions: {
    display: 'flex',
    gap: '12px',
    marginTop: '16px',
  },
});

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error('ErrorBoundary 捕获到错误:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // 这里可以将错误信息发送到错误报告服务
    // logErrorToService(error, errorInfo);
  }

  handleReload = () => {
    // 重新加载页面
    window.location.reload();
  };

  handleReset = () => {
    // 重置错误状态
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义的 fallback UI，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认的错误 UI
      return <ErrorBoundaryUI 
        error={this.state.error}
        errorInfo={this.state.errorInfo}
        onReload={this.handleReload}
        onReset={this.handleReset}
      />;
    }

    return this.props.children;
  }
}

interface ErrorBoundaryUIProps {
  error: Error | null;
  errorInfo: ErrorInfo | null;
  onReload: () => void;
  onReset: () => void;
}

const ErrorBoundaryUI: React.FC<ErrorBoundaryUIProps> = ({ 
  error, 
  errorInfo, 
  onReload, 
  onReset 
}) => {
  const styles = useStyles();

  const formatErrorStack = (error: Error | null, errorInfo: ErrorInfo | null) => {
    let details = '';
    
    if (error) {
      details += `错误: ${error.name}\n`;
      details += `消息: ${error.message}\n\n`;
      
      if (error.stack) {
        details += `堆栈跟踪:\n${error.stack}\n\n`;
      }
    }
    
    if (errorInfo?.componentStack) {
      details += `组件堆栈:\n${errorInfo.componentStack}`;
    }
    
    return details;
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <ErrorCircle24Regular className={styles.icon} />
        
        <Text className={styles.title}>应用程序遇到错误</Text>
        
        <Text className={styles.description}>
          很抱歉，应用程序遇到了一个意外错误。这可能是由于网络问题、数据损坏或程序错误导致的。
          您可以尝试重新加载应用或重置当前状态。
        </Text>

        <MessageBar intent="error">
          <MessageBarBody>
            如果问题持续存在，请联系技术支持并提供下方的错误详情。
          </MessageBarBody>
        </MessageBar>

        <div className={styles.actions}>
          <Button
            appearance="primary"
            icon={<ArrowClockwise24Regular />}
            onClick={onReload}
          >
            重新加载应用
          </Button>
          
          <Button
            appearance="secondary"
            icon={<Home24Regular />}
            onClick={onReset}
          >
            重置状态
          </Button>
        </div>

        {(error || errorInfo) && (
          <details style={{ width: '100%', marginTop: '16px' }}>
            <summary style={{ 
              cursor: 'pointer', 
              padding: '8px',
              backgroundColor: 'var(--colorNeutralBackground3)',
              borderRadius: '4px',
              marginBottom: '8px'
            }}>
              查看错误详情
            </summary>
            <div className={styles.errorDetails}>
              <pre>{formatErrorStack(error, errorInfo)}</pre>
            </div>
          </details>
        )}
      </div>
    </div>
  );
};

export default ErrorBoundary;
