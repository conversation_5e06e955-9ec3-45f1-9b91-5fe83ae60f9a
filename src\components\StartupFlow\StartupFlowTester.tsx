/**
 * 启动流程测试组件
 * 用于测试各种启动场景和错误情况
 */

import React, { useState } from 'react';
import {
  makeStyles,
  Button,
  Card,
  Title2,
  Text,
  Dropdown,
  Option,
  Switch,
  Field,
  MessageBar,
  MessageBarBody,
  tokens,
} from '@fluentui/react-components';
import {
  Play24Regular,
  Stop24Regular,
  ArrowReset24Regular,
  Bug24Regular,
} from '@fluentui/react-icons';
import { StrictStartupPhase, useStrictStartupFlowStore } from '../../stores/strictStartupFlowStore';

const useStyles = makeStyles({
  container: {
    position: 'fixed',
    bottom: tokens.spacingVerticalL,
    left: tokens.spacingHorizontalL,
    width: '350px',
    zIndex: 1000,
  },
  card: {
    padding: tokens.spacingVerticalM,
    backgroundColor: tokens.colorNeutralBackground1,
    border: `2px solid ${tokens.colorBrandBorder1}`,
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
    display: 'flex',
    alignItems: 'center',
    gap: tokens.spacingHorizontalS,
  },
  controls: {
    display: 'flex',
    flexDirection: 'column',
    gap: tokens.spacingVerticalS,
  },
  buttonGroup: {
    display: 'flex',
    gap: tokens.spacingHorizontalS,
    flexWrap: 'wrap',
  },
  status: {
    marginTop: tokens.spacingVerticalM,
    padding: tokens.spacingVerticalS,
    backgroundColor: tokens.colorNeutralBackground2,
    borderRadius: tokens.borderRadiusSmall,
    fontSize: tokens.fontSizeBase200,
  },
});

// 测试场景配置
const TEST_SCENARIOS = {
  'normal': '正常启动流程',
  'network-fail': '网络连接失败',
  'version-fail': '版本检查失败',
  'privacy-reject': '拒绝隐私政策',
  'activation-fail': '激活码验证失败',
  'activation-expired': '激活码已过期',
  'device-mismatch': '设备指纹不匹配',
  'slow-network': '网络连接缓慢',
  'partial-failure': '部分阶段失败',
};

interface StartupFlowTesterProps {
  isVisible?: boolean;
  onScenarioTest?: (scenario: string) => void;
}

const StartupFlowTester: React.FC<StartupFlowTesterProps> = ({
  isVisible = false,
  onScenarioTest,
}) => {
  const styles = useStyles();
  const [selectedScenario, setSelectedScenario] = useState<string>('normal');
  const [isTestMode, setIsTestMode] = useState(false);
  const [autoRetry, setAutoRetry] = useState(false);
  const [simulateDelay, setSimulateDelay] = useState(false);

  const {
    currentPhase,
    isInitialized,
    hasError,
    getFlowSummary,
    resetFlow,
    initializeFlow,
  } = useStrictStartupFlowStore();

  // 仅在开发环境显示
  if (process.env.NODE_ENV !== 'development' || !isVisible) {
    return null;
  }

  const handleStartTest = () => {
    console.log(`🧪 开始测试场景: ${selectedScenario}`);
    
    // 重置流程
    resetFlow();
    
    // 设置测试模式
    setIsTestMode(true);
    
    // 模拟测试场景
    simulateTestScenario(selectedScenario);
    
    // 通知父组件
    if (onScenarioTest) {
      onScenarioTest(selectedScenario);
    }
  };

  const handleStopTest = () => {
    console.log('🛑 停止测试');
    setIsTestMode(false);
    resetFlow();
  };

  const handleResetFlow = () => {
    console.log('🔄 重置启动流程');
    resetFlow();
    setIsTestMode(false);
  };

  const simulateTestScenario = (scenario: string) => {
    // 设置测试环境变量
    (window as any).__HOUT_TEST_MODE__ = true;
    (window as any).__HOUT_TEST_SCENARIO__ = scenario;
    (window as any).__HOUT_TEST_AUTO_RETRY__ = autoRetry;
    (window as any).__HOUT_TEST_SIMULATE_DELAY__ = simulateDelay;

    // 根据场景设置特定的测试参数
    switch (scenario) {
      case 'network-fail':
        (window as any).__HOUT_TEST_NETWORK_FAIL__ = true;
        break;
      case 'version-fail':
        (window as any).__HOUT_TEST_VERSION_FAIL__ = true;
        break;
      case 'privacy-reject':
        (window as any).__HOUT_TEST_PRIVACY_REJECT__ = true;
        break;
      case 'activation-fail':
        (window as any).__HOUT_TEST_ACTIVATION_FAIL__ = true;
        break;
      case 'activation-expired':
        (window as any).__HOUT_TEST_ACTIVATION_EXPIRED__ = true;
        break;
      case 'device-mismatch':
        (window as any).__HOUT_TEST_DEVICE_MISMATCH__ = true;
        break;
      case 'slow-network':
        (window as any).__HOUT_TEST_SLOW_NETWORK__ = true;
        break;
      case 'partial-failure':
        (window as any).__HOUT_TEST_PARTIAL_FAILURE__ = true;
        break;
      default:
        // 正常流程，清除所有测试标志
        Object.keys(window).forEach(key => {
          if (key.startsWith('__HOUT_TEST_') && key !== '__HOUT_TEST_MODE__') {
            delete (window as any)[key];
          }
        });
        break;
    }

    // 启动流程
    setTimeout(() => {
      initializeFlow();
    }, 100);
  };

  const getStatusColor = () => {
    if (hasError) return tokens.colorPaletteRedBackground1;
    if (isTestMode) return tokens.colorPaletteYellowBackground1;
    if (isInitialized) return tokens.colorPaletteGreenBackground1;
    return tokens.colorNeutralBackground2;
  };

  const flowSummary = getFlowSummary();

  return (
    <div className={styles.container}>
      <Card className={styles.card}>
        <div className={styles.header}>
          <Bug24Regular style={{ color: tokens.colorBrandForeground1 }} />
          <Title2>启动流程测试</Title2>
        </div>

        <div className={styles.controls}>
          <Field label="测试场景">
            <Dropdown
              value={TEST_SCENARIOS[selectedScenario as keyof typeof TEST_SCENARIOS]}
              onOptionSelect={(_, data) => {
                const scenario = Object.keys(TEST_SCENARIOS).find(
                  key => TEST_SCENARIOS[key as keyof typeof TEST_SCENARIOS] === data.optionText
                );
                if (scenario) {
                  setSelectedScenario(scenario);
                }
              }}
            >
              {Object.entries(TEST_SCENARIOS).map(([key, label]) => (
                <Option key={key} text={label}>
                  {label}
                </Option>
              ))}
            </Dropdown>
          </Field>

          <Field label="测试选项">
            <div style={{ display: 'flex', flexDirection: 'column', gap: tokens.spacingVerticalXS }}>
              <Switch
                checked={autoRetry}
                onChange={(_, data) => setAutoRetry(data.checked)}
                label="自动重试"
              />
              <Switch
                checked={simulateDelay}
                onChange={(_, data) => setSimulateDelay(data.checked)}
                label="模拟延迟"
              />
            </div>
          </Field>

          <div className={styles.buttonGroup}>
            <Button
              appearance="primary"
              icon={<Play24Regular />}
              onClick={handleStartTest}
              disabled={isTestMode}
            >
              开始测试
            </Button>
            
            <Button
              appearance="secondary"
              icon={<Stop24Regular />}
              onClick={handleStopTest}
              disabled={!isTestMode}
            >
              停止测试
            </Button>
            
            <Button
              appearance="outline"
              icon={<ArrowReset24Regular />}
              onClick={handleResetFlow}
            >
              重置
            </Button>
          </div>

          {isTestMode && (
            <MessageBar intent="info">
              <MessageBarBody>
                测试模式已启用 - 场景: {TEST_SCENARIOS[selectedScenario as keyof typeof TEST_SCENARIOS]}
              </MessageBarBody>
            </MessageBar>
          )}
        </div>

        <div className={styles.status} style={{ backgroundColor: getStatusColor() }}>
          <Text weight="semibold">状态信息</Text>
          <br />
          <Text>当前阶段: {currentPhase}</Text>
          <br />
          <Text>进度: {Math.round(flowSummary.progress)}%</Text>
          <br />
          <Text>会话ID: {flowSummary.sessionId.substring(0, 12)}...</Text>
          <br />
          <Text>运行时间: {Math.round(flowSummary.duration / 1000)}秒</Text>
          {hasError && (
            <>
              <br />
              <Text style={{ color: tokens.colorPaletteRedForeground1 }}>
                错误: {flowSummary.lastError}
              </Text>
            </>
          )}
        </div>
      </Card>
    </div>
  );
};

export default StartupFlowTester;
