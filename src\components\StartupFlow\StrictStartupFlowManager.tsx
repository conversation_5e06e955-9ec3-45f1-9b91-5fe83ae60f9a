/**
 * 严格启动流程管理器
 * 实现严格的启动流程验证，不允许降级策略和离线模式
 * 
 * 启动流程序列：
 * 1. 网络连接检查（必须成功）
 * 2. 版本检查和公告显示（必须成功）
 * 3. 首次启动检测
 * 4. 隐私政策确认（仅首次启动）
 * 5. 激活码验证（必须成功）
 * 6. 进入主界面
 * 7. 数据收集初始化
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  makeStyles,
  Text,
  Title2,
  Card,
  MessageBar,
  MessageBarBody,
  tokens,
} from '@fluentui/react-components';
import { Warning24Regular } from '@fluentui/react-icons';

import { useStartupFlowStore } from '../../stores/startupFlowStore';
import { usePrivacyConsentStore, shouldShowPrivacyConsent, shouldExitApplication } from '../../stores/privacyConsentStore';
import { SecurityConfigManager } from '../../config/securityConfig';

import StrictNetworkChecker from './StrictNetworkChecker';
import StrictVersionChecker from './EnhancedVersionChecker';
import PrivacyConsentDialog from '../Privacy/PrivacyConsentDialog';
import ActivationPage from '../Welcome/pages/ActivationPage';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100vh',
    padding: tokens.spacingVerticalXXL,
    backgroundColor: tokens.colorNeutralBackground1,
  },
  errorCard: {
    maxWidth: '500px',
    width: '100%',
    padding: tokens.spacingVerticalXL,
    textAlign: 'center',
    backgroundColor: tokens.colorPaletteRedBackground1,
    borderColor: tokens.colorPaletteRedBorder1,
  },
  errorTitle: {
    color: tokens.colorPaletteRedForeground1,
    marginBottom: tokens.spacingVerticalM,
  },
  errorMessage: {
    color: tokens.colorPaletteRedForeground2,
  },
});

export type StrictStartupPhase =
  | 'network-check'           // 网络连接检查
  | 'version-check'           // 版本检查和公告显示
  | 'first-launch-detection'  // 首次启动检测
  | 'privacy-consent'         // 隐私政策确认
  | 'activation-verification' // 激活码验证
  | 'main-app'                // 进入主界面
  | 'data-collection'         // 数据收集初始化
  | 'completed'               // 完成
  | 'error';                  // 错误状态

interface StrictStartupFlowManagerProps {
  onComplete: () => void;
  onError: (error: string) => void;
}

const StrictStartupFlowManager: React.FC<StrictStartupFlowManagerProps> = ({
  onComplete,
  onError,
}) => {
  const styles = useStyles();
  const [currentPhase, setCurrentPhase] = useState<StrictStartupPhase>('network-check');
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [networkResult, setNetworkResult] = useState<any>(null);
  const [showPrivacyConsent, setShowPrivacyConsent] = useState(false);
  const [showActivationValidator, setShowActivationValidator] = useState(false);

  const {
    setCurrentPhase: setStorePhase,
    setIsFirstLaunch,
    setFirstLaunchDetected,
    setPrivacyConsentCompleted,
    setActivationVerified,
    setVersionCheckCompleted,
    setAnnouncementDisplayed,
    updateUserSettings,
    setActivationStatus,
  } = useStartupFlowStore();

  const privacyConsentStore = usePrivacyConsentStore();

  // 初始化启动流程
  const initializeStartupFlow = useCallback(async () => {
    try {
      console.log('🚀 开始严格启动流程...');

      // 初始化安全配置
      console.log('🔐 初始化安全配置...');
      const securityConfig = SecurityConfigManager.getInstance();
      await securityConfig.initialize();
      console.log('✅ 安全配置初始化完成');

      // 开始启动流程：阶段1 - 网络连接检查
      setCurrentPhase('network-check');
      setStorePhase('network-check');
      setIsInitialized(true);

    } catch (error) {
      console.error('❌ 启动流程初始化失败:', error);
      const errorMessage = error instanceof Error ? error.message : '初始化失败';
      setError(errorMessage);
      setCurrentPhase('error');
      onError(errorMessage);
    }
  }, [setStorePhase, onError]);

  // 组件挂载时初始化
  useEffect(() => {
    initializeStartupFlow();
  }, [initializeStartupFlow]);

  // 处理网络检查完成
  const handleNetworkCheckComplete = useCallback((result: any) => {
    console.log('✅ 网络检查完成:', result);
    setNetworkResult(result);
    setCurrentPhase('version-check');
    setStorePhase('version-check');
  }, [setStorePhase]);

  // 处理网络检查错误
  const handleNetworkCheckError = useCallback((error: string) => {
    console.error('❌ 网络检查失败:', error);
    setError(error);
    setCurrentPhase('error');
    onError(error);
  }, [onError]);

  // 处理版本检查完成
  const handleVersionCheckComplete = useCallback((result: any) => {
    console.log('✅ 版本检查完成:', result);
    setVersionCheckCompleted(true);
    setAnnouncementDisplayed(true);
    setCurrentPhase('first-launch-detection');
    setStorePhase('first-launch-detection');
    
    // 立即执行首次启动检测
    handleFirstLaunchDetection();
  }, [setVersionCheckCompleted, setAnnouncementDisplayed, setStorePhase]);

  // 处理版本检查错误
  const handleVersionCheckError = useCallback((error: string) => {
    console.error('❌ 版本检查失败:', error);
    setError(error);
    setCurrentPhase('error');
    onError(error);
  }, [onError]);

  // 处理首次启动检测
  const handleFirstLaunchDetection = useCallback(() => {
    console.log('🔍 开始首次启动检测...');

    // 检查是否为首次启动
    const isFirstTime = privacyConsentStore.isFirstLaunch || !privacyConsentStore.hasCompletedPrivacySetup;

    console.log('首次启动检测结果:', {
      isFirstLaunch: privacyConsentStore.isFirstLaunch,
      hasCompletedPrivacySetup: privacyConsentStore.hasCompletedPrivacySetup,
      isFirstTime,
    });

    setIsFirstLaunch(isFirstTime);
    setFirstLaunchDetected(true);

    if (isFirstTime) {
      console.log('✅ 检测到首次启动，进入隐私政策确认阶段');
      setCurrentPhase('privacy-consent');
      setStorePhase('privacy-consent');
      handlePrivacyConsentPhase();
    } else {
      console.log('✅ 非首次启动，直接进入激活验证阶段');
      setCurrentPhase('activation-verification');
      setStorePhase('activation-verification');
      handleActivationVerificationPhase();
    }
  }, [privacyConsentStore, setIsFirstLaunch, setFirstLaunchDetected, setStorePhase]);

  // 处理隐私政策确认阶段
  const handlePrivacyConsentPhase = useCallback(() => {
    console.log('📋 进入隐私政策确认阶段');

    // 检查是否需要显示隐私政策同意界面
    const needsPrivacyConsent = shouldShowPrivacyConsent();
    const shouldExit = shouldExitApplication();

    if (shouldExit) {
      console.log('🚫 用户撤销了必要同意，应用将退出');
      handlePrivacyConsentReject();
      return;
    }

    if (needsPrivacyConsent) {
      console.log('📋 显示隐私政策同意界面');
      setShowPrivacyConsent(true);
    } else {
      console.log('✅ 隐私政策已同意，进入激活验证阶段');
      setPrivacyConsentCompleted(true);
      setCurrentPhase('activation-verification');
      setStorePhase('activation-verification');
      handleActivationVerificationPhase();
    }
  }, [setPrivacyConsentCompleted, setStorePhase]);

  // 处理激活验证阶段
  const handleActivationVerificationPhase = useCallback(() => {
    console.log('🔑 进入激活验证阶段');
    setShowActivationValidator(true);
  }, []);

  // 处理隐私政策同意
  const handlePrivacyConsentAccept = useCallback(() => {
    console.log('✅ 用户同意隐私政策');
    setShowPrivacyConsent(false);
    setPrivacyConsentCompleted(true);
    setCurrentPhase('activation-verification');
    setStorePhase('activation-verification');
    handleActivationVerificationPhase();
  }, [setPrivacyConsentCompleted, setStorePhase, handleActivationVerificationPhase]);

  // 处理隐私政策拒绝
  const handlePrivacyConsentReject = useCallback(async () => {
    console.log('🚫 用户拒绝隐私政策，应用将退出');
    setShowPrivacyConsent(false);
    setError('用户拒绝隐私政策，应用无法继续运行');
    setCurrentPhase('error');

    try {
      const { exit } = await import('@tauri-apps/api/app');
      await exit(0);
    } catch (error) {
      console.error('退出应用失败:', error);
      window.close();
    }
  }, []);

  // 处理激活验证完成
  const handleActivationComplete = useCallback(() => {
    console.log('✅ 激活验证完成');
    setShowActivationValidator(false);
    setActivationVerified(true);
    setCurrentPhase('main-app');
    setStorePhase('main-app');
    
    // 进入主应用
    setTimeout(() => {
      setCurrentPhase('completed');
      setStorePhase('completed');
      onComplete();
    }, 1000);
  }, [setActivationVerified, setStorePhase, onComplete]);

  // 处理激活验证错误
  const handleActivationError = useCallback((error: string) => {
    console.error('❌ 激活验证失败:', error);
    setShowActivationValidator(false);
    setError(error);
    setCurrentPhase('error');
    onError(error);
  }, [onError]);

  // 渲染当前阶段
  const renderCurrentPhase = () => {
    switch (currentPhase) {
      case 'network-check':
        return (
          <StrictNetworkChecker
            onComplete={handleNetworkCheckComplete}
            onError={handleNetworkCheckError}
          />
        );

      case 'version-check':
        return (
          <StrictVersionChecker
            onComplete={handleVersionCheckComplete}
            onError={handleVersionCheckError}
            networkResult={networkResult}
          />
        );

      case 'first-launch-detection':
        // 这个阶段在后台进行，不需要UI
        return null;

      case 'privacy-consent':
        // 隐私政策同意由对话框处理
        return null;

      case 'activation-verification':
        return null; // 由ActivationValidator对话框处理

      case 'main-app':
      case 'data-collection':
      case 'completed':
        // 这些阶段在后台进行，不需要UI
        return null;

      case 'error':
        return (
          <Card className={styles.errorCard}>
            <Warning24Regular style={{ fontSize: '48px', color: tokens.colorPaletteRedForeground1 }} />
            <Title2 className={styles.errorTitle}>启动流程失败</Title2>
            <Text className={styles.errorMessage}>
              {error || '未知错误'}
            </Text>
          </Card>
        );

      default:
        return null;
    }
  };

  if (!isInitialized) {
    return null;
  }

  return (
    <div className={styles.container}>
      {renderCurrentPhase()}
      
      {/* 隐私政策同意对话框 */}
      <PrivacyConsentDialog
        open={showPrivacyConsent}
        onAccept={handlePrivacyConsentAccept}
        onReject={handlePrivacyConsentReject}
      />

      {/* 激活验证页面 */}
      {showActivationValidator && (
        <ActivationPage
          onSuccess={handleActivationComplete}
          onError={handleActivationError}
        />
      )}
    </div>
  );
};

export default StrictStartupFlowManager;
