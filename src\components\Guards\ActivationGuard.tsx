/**
 * 激活码验证守卫组件
 * 在应用启动时强制检查激活码状态，确保只有激活用户才能访问主功能
 */

import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import {
  makeStyles,
  Text,
  Spinner,
  MessageBar,
  MessageBarBody,
  Button,
} from '@fluentui/react-components';
import {
  Warning24Regular,
  Shield24Regular,
  ErrorCircle24Regular,
} from '@fluentui/react-icons';
import { activationService } from '../../services/activationService';
import { useAppConfigStore } from '../../stores/welcomeStore';
import { useStartupFlowStore } from '../../stores/startupFlowStore';
import SmoothTransition from '../Common/SmoothTransition';

const useStyles = makeStyles({
  container: {
    width: '100%',
    height: '100vh',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '24px',
    padding: '40px',
    backgroundColor: 'var(--colorNeutralBackground1)',
    background: 'linear-gradient(135deg, var(--colorNeutralBackground1) 0%, var(--colorNeutralBackground2) 100%)',
    transition: 'opacity 0.3s ease-in-out',
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '16px',
    maxWidth: '500px',
    textAlign: 'center',
    opacity: 1,
    transform: 'translateY(0)',
    transition: 'opacity 0.4s ease-in-out, transform 0.4s ease-in-out',
  },
  contentEntering: {
    opacity: 0,
    transform: 'translateY(20px)',
  },
  contentExiting: {
    opacity: 0,
    transform: 'translateY(-20px)',
  },
  icon: {
    fontSize: '64px',
    color: 'var(--colorBrandForeground1)',
  },
  title: {
    fontSize: '24px',
    fontWeight: '600',
    color: 'var(--colorNeutralForeground1)',
  },
  description: {
    fontSize: '16px',
    color: 'var(--colorNeutralForeground2)',
    lineHeight: '1.5',
  },
  errorContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '16px',
    padding: '24px',
    backgroundColor: 'var(--colorPaletteRedBackground1)',
    borderRadius: '8px',
    border: '1px solid var(--colorPaletteRedBorder1)',
  },
  errorIcon: {
    fontSize: '48px',
    color: 'var(--colorPaletteRedForeground1)',
  },
  actions: {
    display: 'flex',
    gap: '12px',
    marginTop: '16px',
  },
});

interface ActivationGuardProps {
  children: React.ReactNode;
  onActivationRequired: () => void;
}

type ActivationCheckStatus = 'checking' | 'valid' | 'invalid' | 'expired' | 'error';

const ActivationGuard: React.FC<ActivationGuardProps> = ({ children, onActivationRequired }) => {
  const styles = useStyles();
  const [status, setStatus] = useState<ActivationCheckStatus>('checking');
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [showSkipOption, setShowSkipOption] = useState(false);
  const [checkDuration, setCheckDuration] = useState(0);

  const { config } = useAppConfigStore();
  const { setCurrentPhase } = useStartupFlowStore();

  // 使用ref来防止重复检查和状态抖动
  const isCheckingRef = useRef(false);
  const statusChangeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastCheckTimeRef = useRef<number>(0);
  const mountedRef = useRef(true);

  // 防抖的状态更新函数
  const updateStatusWithDelay = useCallback((newStatus: ActivationCheckStatus, delay: number = 300) => {
    if (statusChangeTimeoutRef.current) {
      clearTimeout(statusChangeTimeoutRef.current);
    }

    statusChangeTimeoutRef.current = setTimeout(() => {
      if (mountedRef.current) {
        setStatus(newStatus);
      }
    }, delay);
  }, []);

  // 检查激活状态 - 添加真正的超时保护和性能优化
  const checkActivationStatus = useCallback(async () => {
    const now = Date.now();
    const checkId = Math.random().toString(36).substr(2, 9);

    console.log(`🔍 ActivationGuard[${checkId}]: 开始检查激活状态...`);

    // 防止频繁检查（最少间隔1秒）
    if (now - lastCheckTimeRef.current < 1000) {
      console.log(`🔍 ActivationGuard[${checkId}]: 跳过重复检查（间隔太短: ${now - lastCheckTimeRef.current}ms）`);
      return;
    }

    // 防止并发检查
    if (isCheckingRef.current) {
      console.log(`🔍 ActivationGuard[${checkId}]: 跳过重复检查（正在检查中）`);
      return;
    }

    isCheckingRef.current = true;
    lastCheckTimeRef.current = now;

    // 立即设置为检查状态
    if (status !== 'checking') {
      console.log(`🔍 ActivationGuard[${checkId}]: 设置状态为checking`);
      setStatus('checking');
    }
    setError(null);

    try {
      // 创建超时保护 - 5秒绝对超时
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          console.error(`⏰ ActivationGuard[${checkId}]: 检查超时（5秒）`);
          reject(new Error('激活状态检查超时（5秒）'));
        }, 5000);
      });

      // 创建实际的检查Promise
      const checkPromise = new Promise<any>((resolve, reject) => {
        try {
          console.log(`🔍 ActivationGuard[${checkId}]: 开始加载本地激活数据...`);
          const startTime = Date.now();

          // 同步调用，但包装在Promise中以便超时控制
          const localData = activationService.loadActivationData();
          const loadTime = Date.now() - startTime;
          console.log(`🔍 ActivationGuard[${checkId}]: 本地数据加载完成，耗时: ${loadTime}ms`);

          if (!localData || !localData.isActivated) {
            console.log(`❌ ActivationGuard[${checkId}]: 未找到本地激活数据`);
            resolve({ result: 'invalid', reason: '未找到激活数据' });
            return;
          }

          console.log(`🔍 ActivationGuard[${checkId}]: 开始检查激活状态...`);
          const checkStartTime = Date.now();

          // checkActivationStatus是同步方法
          const activationInfo = activationService.checkActivationStatus();
          const checkTime = Date.now() - checkStartTime;
          console.log(`🔍 ActivationGuard[${checkId}]: 激活状态检查完成，耗时: ${checkTime}ms`);

          if (!activationInfo.isActivated) {
            console.log(`❌ ActivationGuard[${checkId}]: 激活状态无效`);
            resolve({ result: 'invalid', reason: activationInfo.expiredReason || '激活状态无效' });
            return;
          }

          if (activationInfo.isExpired) {
            console.log(`⏰ ActivationGuard[${checkId}]: 激活已过期`);
            resolve({ result: 'expired', reason: activationInfo.expiredReason || '激活已过期' });
            return;
          }

          console.log(`✅ ActivationGuard[${checkId}]: 激活状态有效`);
          resolve({ result: 'valid', activationInfo });

        } catch (err) {
          console.error(`❌ ActivationGuard[${checkId}]: 检查过程中发生错误:`, err);
          reject(err);
        }
      });

      // 使用Promise.race实现真正的超时控制
      const result = await Promise.race([checkPromise, timeoutPromise]);

      // 添加最小显示时间，避免闪烁（但不阻塞超时）
      const minDisplayTime = new Promise(resolve => setTimeout(resolve, 500));
      await minDisplayTime;

      if (!mountedRef.current) {
        console.log(`🔍 ActivationGuard[${checkId}]: 组件已卸载，跳过状态更新`);
        return;
      }

      // 根据结果更新状态
      switch (result.result) {
        case 'valid':
          console.log(`✅ ActivationGuard[${checkId}]: 更新状态为valid`);
          updateStatusWithDelay('valid', 100);
          break;
        case 'invalid':
          console.log(`❌ ActivationGuard[${checkId}]: 更新状态为invalid - ${result.reason}`);
          updateStatusWithDelay('invalid', 100);
          break;
        case 'expired':
          console.log(`⏰ ActivationGuard[${checkId}]: 更新状态为expired - ${result.reason}`);
          updateStatusWithDelay('expired', 100);
          break;
      }

    } catch (err) {
      console.error(`❌ ActivationGuard[${checkId}]: 检查激活状态失败:`, err);
      const errorMessage = err instanceof Error ? err.message : '检查激活状态失败';

      if (mountedRef.current) {
        setError(errorMessage);
        updateStatusWithDelay('error', 100);
      }
    } finally {
      isCheckingRef.current = false;
      console.log(`🔍 ActivationGuard[${checkId}]: 检查完成，释放锁定`);
    }
  }, [status, updateStatusWithDelay]);

  // 处理需要激活的情况 - 防止重复调用
  const handleActivationRequired = useCallback(() => {
    if (!mountedRef.current) {
      console.log('🔄 ActivationGuard: 组件已卸载，跳过跳转');
      return;
    }

    console.log('🔄 ActivationGuard: 需要激活，跳转到激活页面');

    try {
      setCurrentPhase('activation-verification');
      onActivationRequired();
    } catch (err) {
      console.error('🔄 ActivationGuard: 跳转到激活页面失败:', err);
    }
  }, [setCurrentPhase, onActivationRequired]);

  // 处理跳过验证
  const handleSkipVerification = useCallback(() => {
    console.log('⚠️ ActivationGuard: 用户选择跳过激活验证');
    if (mountedRef.current) {
      handleActivationRequired();
    }
  }, [handleActivationRequired]);

  // 处理强制进入（开发模式）
  const handleForceEntry = useCallback(() => {
    console.log('🔓 ActivationGuard: 强制进入应用（开发模式）');
    if (mountedRef.current) {
      updateStatusWithDelay('valid', 100);
    }
  }, [updateStatusWithDelay]);

  // 处理重试 - 添加防抖和跳过选项
  const handleRetry = useCallback(() => {
    if (!mountedRef.current) return;

    if (retryCount < 3) {
      console.log(`🔄 ActivationGuard: 重试检查激活状态 (${retryCount + 1}/3)`);
      setRetryCount(prev => prev + 1);
      setError(null);
      setShowSkipOption(false);

      // 延迟重试，避免频繁调用
      setTimeout(() => {
        if (mountedRef.current) {
          checkActivationStatus();
        }
      }, 1000);
    } else {
      console.log('❌ ActivationGuard: 重试次数已达上限，显示跳过选项');
      setShowSkipOption(true);
    }
  }, [retryCount, checkActivationStatus]);

  // 性能监控 - 检查耗时
  useEffect(() => {
    if (status === 'checking') {
      const startTime = Date.now();
      setCheckDuration(0);

      const timer = setInterval(() => {
        const duration = Date.now() - startTime;
        setCheckDuration(duration);

        // 如果检查超过10秒，显示跳过选项
        if (duration > 10000 && !showSkipOption) {
          console.warn('⚠️ ActivationGuard: 检查时间过长，显示跳过选项');
          setShowSkipOption(true);
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [status, showSkipOption]);

  // 组件挂载时检查激活状态 - 简化逻辑，移除复杂依赖
  useEffect(() => {
    mountedRef.current = true;

    console.log('🚀 ActivationGuard: 组件挂载，准备检查激活状态');

    // 使用全局锁定机制，避免重复检查
    if ((window as any).__activationGuardMounted) {
      console.log('🚀 ActivationGuard: 已有实例在运行，跳过检查');
      return () => {
        mountedRef.current = false;
      };
    }

    (window as any).__activationGuardMounted = true;

    // 延迟执行，避免与其他组件的初始化冲突
    const initTimer = setTimeout(() => {
      if (mountedRef.current) {
        console.log('🚀 ActivationGuard: 开始初始激活检查');
        checkActivationStatus();
      }
    }, 200);

    return () => {
      console.log('🚀 ActivationGuard: 组件卸载，清理资源');
      mountedRef.current = false;
      (window as any).__activationGuardMounted = false;
      clearTimeout(initTimer);
      if (statusChangeTimeoutRef.current) {
        clearTimeout(statusChangeTimeoutRef.current);
      }
    };
  }, []); // 移除所有依赖，只在挂载时执行一次

  // 当状态为无效或过期时，不自动跳转，而是显示激活界面
  useEffect(() => {
    if (status === 'invalid' || status === 'expired') {
      console.log(`🔄 ActivationGuard: 状态变为 ${status}，显示激活界面`);
      // 不再自动跳转，而是在UI中显示激活选项
    }
  }, [status]); // 只依赖status

  // 如果激活有效，显示子组件
  if (status === 'valid') {
    return (
      <SmoothTransition show={true} duration={400}>
        {children}
      </SmoothTransition>
    );
  }

  // 渲染检查状态界面
  const renderStatusContent = () => {
    switch (status) {
      case 'checking':
        return (
          <div className={styles.content}>
            <Shield24Regular className={styles.icon} />
            <Text className={styles.title}>正在验证激活状态</Text>
            <Text className={styles.description}>
              正在检查您的激活码状态，请稍候...
            </Text>
            {checkDuration > 0 && (
              <Text size={200} style={{ color: 'var(--colorNeutralForeground2)', marginTop: '8px' }}>
                已耗时: {Math.floor(checkDuration / 1000)}秒
              </Text>
            )}
            <Spinner size="large" />

            {showSkipOption && (
              <div style={{ marginTop: '16px', display: 'flex', gap: '8px' }}>
                <Button
                  appearance="secondary"
                  onClick={handleSkipVerification}
                >
                  跳过验证
                </Button>
                {process.env.NODE_ENV === 'development' && (
                  <Button
                    appearance="outline"
                    onClick={handleForceEntry}
                  >
                    强制进入（开发）
                  </Button>
                )}
              </div>
            )}
          </div>
        );

      case 'invalid':
        return (
          <div className={styles.content}>
            <Warning24Regular className={styles.icon} style={{ color: 'var(--colorPaletteRedForeground1)' }} />
            <Text className={styles.title}>需要激活</Text>
            <Text className={styles.description}>
              未检测到有效的激活码，请输入激活码以继续使用应用。
            </Text>
            <MessageBar intent="warning">
              <MessageBarBody>
                请点击下方按钮进入激活页面输入激活码。
              </MessageBarBody>
            </MessageBar>

            <div className={styles.actions}>
              <Button
                appearance="primary"
                onClick={handleActivationRequired}
              >
                前往激活页面
              </Button>

              <Button
                appearance="secondary"
                onClick={handleRetry}
              >
                重新检查
              </Button>

              {process.env.NODE_ENV === 'development' && (
                <Button
                  appearance="outline"
                  onClick={handleForceEntry}
                >
                  强制进入（开发）
                </Button>
              )}
            </div>
          </div>
        );

      case 'expired':
        return (
          <div className={styles.content}>
            <Warning24Regular className={styles.icon} style={{ color: 'var(--colorPaletteYellowForeground1)' }} />
            <Text className={styles.title}>激活已过期</Text>
            <Text className={styles.description}>
              您的激活码已过期，请重新输入有效的激活码以继续使用应用。
            </Text>
            <MessageBar intent="warning">
              <MessageBarBody>
                请点击下方按钮进入激活页面重新激活。
              </MessageBarBody>
            </MessageBar>

            <div className={styles.actions}>
              <Button
                appearance="primary"
                onClick={handleActivationRequired}
              >
                前往激活页面
              </Button>

              <Button
                appearance="secondary"
                onClick={handleRetry}
              >
                重新检查
              </Button>

              {process.env.NODE_ENV === 'development' && (
                <Button
                  appearance="outline"
                  onClick={handleForceEntry}
                >
                  强制进入（开发）
                </Button>
              )}
            </div>
          </div>
        );

      case 'error':
        return (
          <div className={styles.content}>
            <div className={styles.errorContainer}>
              <ErrorCircle24Regular className={styles.errorIcon} />
              <Text className={styles.title}>验证失败</Text>
              <Text className={styles.description}>
                检查激活状态时发生错误：{error}
              </Text>

              {checkDuration > 0 && (
                <Text size={200} style={{ color: 'var(--colorNeutralForeground2)', marginTop: '8px' }}>
                  检查耗时: {Math.floor(checkDuration / 1000)}秒
                </Text>
              )}

              <div className={styles.actions}>
                {retryCount < 3 ? (
                  <Button
                    appearance="primary"
                    onClick={handleRetry}
                  >
                    重试 ({retryCount}/3)
                  </Button>
                ) : (
                  <Text size={300} style={{ color: 'var(--colorPaletteRedForeground1)' }}>
                    重试次数已达上限
                  </Text>
                )}

                <Button
                  appearance="secondary"
                  onClick={handleSkipVerification}
                >
                  跳过验证
                </Button>

                {process.env.NODE_ENV === 'development' && (
                  <Button
                    appearance="outline"
                    onClick={handleForceEntry}
                  >
                    强制进入（开发）
                  </Button>
                )}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={styles.container}>
      <SmoothTransition show={true} duration={400} delay={100}>
        {renderStatusContent()}
      </SmoothTransition>
    </div>
  );
};

export default ActivationGuard;
